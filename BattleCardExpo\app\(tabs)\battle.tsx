import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  Dimensions,
  Platform
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Cross-platform shadow helper
const createShadowStyle = (elevation: number, shadowColor = '#8b4513', shadowOpacity = 0.3) => {
  if (Platform.OS === 'web') {
    const shadowRadius = elevation * 2;
    const shadowOffset = elevation / 2;
    return {
      boxShadow: `0 ${shadowOffset}px ${shadowRadius}px rgba(139, 69, 19, ${shadowOpacity})`,
    };
  } else {
    return {
      shadowColor,
      shadowOffset: { width: 0, height: elevation / 2 },
      shadowOpacity,
      shadowRadius: elevation * 2,
      elevation,
    };
  }
};

export default function BattleScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const [battleState, setBattleState] = useState<'battle' | 'ended'>('battle');
  const [isPaused, setIsPaused] = useState(false);
  const [battleSpeed, setBattleSpeed] = useState(1);
  
  // Mock battle data
  const [enemyTeam] = useState([
    { id: 'wolf', name: '森林之狼', emoji: '🐺', health: 100, maxHealth: 100, actionBar: 30 },
    { id: 'troll', name: '山丘巨魔', emoji: '🧌', health: 150, maxHealth: 150, actionBar: 10 },
    { id: 'spider', name: '毒蜘蛛', emoji: '🕷️', health: 80, maxHealth: 80, actionBar: 60 }
  ]);
  
  const [playerTeam] = useState([
    { id: 'fire_dragon', name: '火龍戰士', emoji: '🔥', health: 120, maxHealth: 120, actionBar: 45 },
    { id: 'water_mage', name: '水法師', emoji: '💧', health: 85, maxHealth: 100, actionBar: 70 },
    { id: 'earth_knight', name: '大地騎士', emoji: '🌍', health: 140, maxHealth: 140, actionBar: 20 }
  ]);
  
  const [nextAction, setNextAction] = useState<{ name: string; timeLeft: number } | null>(null);

  useEffect(() => {
    // Auto-start battle immediately when screen loads
    console.log('🚀 戰鬥自動開始！');
    
    // Simulate automatic battle progression
    const battleInterval = setInterval(() => {
      if (battleState === 'battle' && !isPaused) {
        setNextAction(prev => {
          if (!prev) return { name: '火龍戰士', timeLeft: 3 };
          const newTimeLeft = prev.timeLeft - (0.1 * battleSpeed);
          if (newTimeLeft <= 0) {
            // Simulate different actions
            const actions = ['火龍戰士', '水法師', '大地騎士', '森林之狼', '山丘巨魔', '毒蜘蛛'];
            const nextActor = actions[Math.floor(Math.random() * actions.length)];
            return { name: nextActor, timeLeft: 2 + Math.random() * 3 };
          }
          return { ...prev, timeLeft: newTimeLeft };
        });
      }
    }, 100);
    
    return () => clearInterval(battleInterval);
  }, [battleState, isPaused, battleSpeed]);

  const handlePauseBattle = () => {
    setIsPaused(!isPaused);
  };

  const handleSpeedChange = () => {
    const speeds = [1, 2, 4];
    const currentIndex = speeds.indexOf(battleSpeed);
    const nextIndex = (currentIndex + 1) % speeds.length;
    setBattleSpeed(speeds[nextIndex]);
  };

  const handleExitBattle = () => {
    Alert.alert(
      '退出戰鬥',
      '確定要退出當前戰鬥嗎？進度將不會保存。',
      [
        { text: '取消', style: 'cancel' },
        { text: '確定', onPress: () => router.back() }
      ]
    );
  };

  const renderCard = (card: any, isEnemy: boolean) => (
    <View key={card.id} style={[styles.battleCard, isEnemy && styles.enemyCard]}>
      <View style={styles.cardHeader}>
        <Text style={styles.cardLevel}>Lv.5</Text>
        <Text style={styles.cardHealth}>❤️{card.health}</Text>
      </View>
      
      <Text style={styles.cardEmoji}>{card.emoji}</Text>
      <Text style={styles.cardName}>{card.name}</Text>
      
      <View style={styles.actionBarContainer}>
        <View 
          style={[
            styles.actionBar, 
            { width: `${card.actionBar}%` },
            isEnemy && styles.enemyActionBar
          ]} 
        />
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#f7f3e9" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleExitBattle} style={styles.exitButton}>
          <Text style={styles.exitText}>❌</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>⚔️ 森林深處 - 自動戰鬥中</Text>
        <View style={styles.headerControls}>
          <TouchableOpacity onPress={handlePauseBattle} style={styles.controlButton}>
            <Text style={styles.controlText}>{isPaused ? '▶️' : '⏸️'}</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Enemy Team */}
      <View style={styles.enemySection}>
        <Text style={styles.teamStatus}>🔴 ENEMY (3/3) ❤️{enemyTeam.reduce((sum, card) => sum + card.health, 0)}</Text>
        <View style={styles.teamContainer}>
          {enemyTeam.map(card => renderCard(card, true))}
        </View>
      </View>

      {/* Action Preview */}
      {nextAction && (
        <View style={styles.actionPreview}>
          <Text style={styles.nextActionText}>
            ⚡ 下一個: {nextAction.name} ({nextAction.timeLeft.toFixed(1)}秒)
          </Text>
          {isPaused && (
            <Text style={styles.pausedText}>⏸️ 戰鬥已暫停</Text>
          )}
        </View>
      )}

      {/* Player Team */}
      <View style={styles.playerSection}>
        <Text style={styles.teamStatus}>🔵 YOUR TEAM (3/3) ❤️{playerTeam.reduce((sum, card) => sum + card.health, 0)}</Text>
        <View style={styles.teamContainer}>
          {playerTeam.map(card => renderCard(card, false))}
        </View>
      </View>

      {/* Battle Controls */}
      <View style={styles.battleControls}>
        <TouchableOpacity style={styles.controlButton}>
          <Text style={styles.controlButtonText}>📊詳情</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.controlButton} onPress={handleSpeedChange}>
          <Text style={styles.controlButtonText}>⏩{battleSpeed}x</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.controlButton} onPress={handlePauseBattle}>
          <Text style={styles.controlButtonText}>{isPaused ? '▶️開始' : '⏸️暫停'}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.controlButton}>
          <Text style={styles.controlButtonText}>📷</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f7f3e9',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: 'rgba(139, 69, 19, 0.2)',
    borderBottomWidth: 2,
    borderBottomColor: '#8b4513',
  },
  exitButton: {
    padding: 8,
  },
  exitText: {
    fontSize: 20,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#8b4513',
    fontFamily: 'Times New Roman',
  },
  headerControls: {
    flexDirection: 'row',
  },
  controlButton: {
    padding: 8,
    marginLeft: 5,
  },
  controlText: {
    fontSize: 16,
  },
  
  // Battle Styles
  enemySection: {
    padding: 15,
    borderBottomWidth: 2,
    borderBottomColor: 'rgba(220, 38, 38, 0.3)',
  },
  playerSection: {
    padding: 15,
    borderTopWidth: 2,
    borderTopColor: 'rgba(59, 130, 246, 0.3)',
  },
  teamStatus: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#8b4513',
    textAlign: 'center',
    marginBottom: 10,
    fontFamily: 'Times New Roman',
  },
  teamContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  battleCard: {
    width: 90,
    height: 120,
    backgroundColor: '#f9f7f4',
    borderWidth: 2,
    borderColor: '#8b4513',
    borderRadius: 8,
    padding: 8,
    alignItems: 'center',
    ...createShadowStyle(4),
  },
  enemyCard: {
    borderColor: '#dc2626',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 5,
  },
  cardLevel: {
    fontSize: 10,
    color: '#8b4513',
    fontWeight: 'bold',
  },
  cardHealth: {
    fontSize: 10,
    color: '#dc2626',
    fontWeight: 'bold',
  },
  cardEmoji: {
    fontSize: 20,
    marginBottom: 5,
  },
  cardName: {
    fontSize: 8,
    color: '#8b4513',
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: 'Times New Roman',
  },
  actionBarContainer: {
    width: '100%',
    height: 4,
    backgroundColor: '#e5e5e5',
    borderRadius: 2,
  },
  actionBar: {
    height: '100%',
    backgroundColor: '#3b82f6',
    borderRadius: 2,
  },
  enemyActionBar: {
    backgroundColor: '#dc2626',
  },
  actionPreview: {
    padding: 10,
    backgroundColor: 'rgba(139, 69, 19, 0.1)',
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: 'rgba(139, 69, 19, 0.3)',
  },
  nextActionText: {
    fontSize: 14,
    color: '#8b4513',
    textAlign: 'center',
    fontWeight: 'bold',
    fontFamily: 'Times New Roman',
  },
  pausedText: {
    fontSize: 12,
    color: '#dc2626',
    textAlign: 'center',
    fontWeight: 'bold',
    fontFamily: 'Times New Roman',
    marginTop: 5,
  },
  battleControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 15,
    backgroundColor: 'rgba(139, 69, 19, 0.1)',
    borderTopWidth: 2,
    borderTopColor: 'rgba(139, 69, 19, 0.3)',
  },
  controlButtonText: {
    fontSize: 12,
    color: '#8b4513',
    fontWeight: 'bold',
    fontFamily: 'Times New Roman',
  },
});