/**
 * ⚔️ 戰鬥系統類型定義
 * 
 * 定義自動戰鬥系統的所有相關類型和接口
 * 基於 docs/game_design.md 中的自動戰鬥機制設計
 */

import { AIBehaviorType, BattleCard, BattleState, Card, StatusEffect } from './index';

// ==================== 戰鬥核心接口 ====================

/**
 * 戰鬥實例接口
 */
export interface Battle {
  id: string;                           // 戰鬥ID
  stageId: string;                      // 關卡ID
  state: BattleState;                   // 戰鬥狀態
  playerTeam: (BattleCard | null)[][];   // 玩家隊伍 (3x3 二維陣列，允許空位 null)
  enemyTeam: (BattleCard | null)[][];    // 敵方隊伍 (3x3 二維陣列，允許空位 null)

  actionQueue: BattleAction[];          // 行動隊列
  battleLog: BattleLogEntry[];          // 戰鬥日誌
  startTime: Date;                      // 開始時間
  endTime?: Date;                       // 結束時間
  winner?: 'player' | 'enemy';          // 勝利方
  battleSpeed: number;                  // 戰鬥速度倍率
  isPaused: boolean;                    // 是否暫停
}

/**
 * 戰鬥行動接口
 */
export interface BattleAction {
  id: string;                           // 行動ID
  actorId: string;                      // 行動者卡牌ID
  // 注意：只有在可行動時才會決定動作，以下欄位在決定前可為未定義
  actionType?: BattleActionType;        // 行動類型（決定時才產生）
  targetIds?: string[];                 // 目標卡牌ID列表（決定時才產生）
  skillId?: string;                     // 使用的技能ID（若為技能）
  damage?: number;                      // 造成傷害
  healing?: number;                     // 治療量
  statusEffects?: StatusEffect[];       // 施加的狀態效果
  executionTime: number;                // 執行時間戳
}

/**
 * 戰鬥行動類型
 */
export enum BattleActionType {
  NORMAL_ATTACK = 'NormalAttack',       // 普通攻擊
  SKILL_ATTACK = 'SkillAttack',         // 技能攻擊
  HEAL = 'Heal',                        // 治療
  BUFF = 'Buff',                        // 增益
  DEBUFF = 'Debuff',                    // 減益
  DEFEND = 'Defend',                    // 防禦
  WAIT = 'Wait'                         // 等待
}

/**
 * 戰鬥日誌條目
 */
export interface BattleLogEntry {
  id: string;                           // 日誌ID
  timestamp: number;                    // 時間戳
  type: BattleLogType;                  // 日誌類型
  actorName: string;                    // 行動者名稱
  targetNames: string[];                // 目標名稱列表
  actionDescription: string;            // 行動描述
  damage?: number;                      // 傷害數值
  healing?: number;                     // 治療數值
  isCritical?: boolean;                 // 是否暴擊
  statusEffects?: string[];             // 狀態效果名稱
}

/**
 * 戰鬥日誌類型
 */
export enum BattleLogType {
  BATTLE_START = 'BattleStart',         // 戰鬥開始
  BATTLE_END = 'BattleEnd',             // 戰鬥結束

  ACTION_EXECUTE = 'ActionExecute',     // 行動執行
  DAMAGE_DEALT = 'DamageDealt',         // 造成傷害
  HEALING_DONE = 'HealingDone',         // 進行治療
  STATUS_APPLIED = 'StatusApplied',     // 狀態施加
  STATUS_REMOVED = 'StatusRemoved',     // 狀態移除
  CARD_DEFEATED = 'CardDefeated',       // 卡牌被擊敗
  SKILL_USED = 'SkillUsed'              // 技能使用
}

// ==================== 戰鬥計算接口 ====================

/**
 * 傷害計算結果
 */
export interface DamageCalculation {
  baseDamage: number;                   // 基礎傷害
  finalDamage: number;                  // 最終傷害
  isCritical: boolean;                  // 是否暴擊
  criticalMultiplier: number;           // 暴擊倍率
  buffModifier: number;                 // 增益修正
  debuffModifier: number;               // 減益修正
  defenseReduction: number;             // 防禦減免
}

/**
 * 治療計算結果
 */
export interface HealingCalculation {
  baseHealing: number;                  // 基礎治療
  finalHealing: number;                 // 最終治療
  buffModifier: number;                 // 增益修正
  overheal: number;                     // 過量治療
  actualHealing: number;                // 實際治療量
}

/**
 * 行動順序計算結果
 */
export interface ActionOrderCalculation {
  cardId: string;                       // 卡牌ID
  actionBar: number;                    // 行動條進度
  speed: number;                        // 當前速度
  speedModifiers: number;               // 速度修正
  nextActionTime: number;               // 下次行動時間
  priority: number;                     // 行動優先級
}

// ==================== AI系統接口 ====================

/**
 * AI決策接口
 */
export interface AIDecision {
  cardId: string;                       // 決策卡牌ID
  actionType: BattleActionType;         // 行動類型
  targetIds: string[];                  // 目標ID列表
  skillId?: string;                     // 技能ID
  confidence: number;                   // 決策信心度 (0-1)
  reasoning: string;                    // 決策理由
  alternativeActions: AIAlternative[];  // 備選行動
}

/**
 * AI備選行動
 */
export interface AIAlternative {
  actionType: BattleActionType;         // 行動類型
  targetIds: string[];                  // 目標ID列表
  skillId?: string;                     // 技能ID
  score: number;                        // 行動評分
  reasoning: string;                    // 選擇理由
}

/**
 * AI評估上下文
 */
export interface AIEvaluationContext {
  battle: Battle;                       // 戰鬥實例
  aiCard: BattleCard;                   // AI控制的卡牌
  alliedCards: BattleCard[];            // 友方卡牌
  enemyCards: BattleCard[];             // 敵方卡牌
  behaviorType: AIBehaviorType;         // AI行為類型

  timeElapsed: number;                  // 已用時間
}

/**
 * 目標評估結果
 */
export interface TargetEvaluation {
  targetId: string;                     // 目標ID
  score: number;                        // 評估分數
  threat: number;                       // 威脅度
  priority: number;                     // 優先級
  distance: number;                     // 距離
  vulnerability: number;                // 脆弱度
  strategicValue: number;               // 戰略價值
}

// ==================== 戰鬥事件接口 ====================

/**
 * 戰鬥事件接口
 */
export interface BattleEvent {
  id: string;                           // 事件ID
  type: BattleEventType;                // 事件類型
  timestamp: number;                    // 時間戳
  data: any;                            // 事件數據
  processed: boolean;                   // 是否已處理
}

/**
 * 戰鬥事件類型
 */
export enum BattleEventType {
  BATTLE_INITIALIZED = 'BattleInitialized',
  BATTLE_STARTED = 'BattleStarted',
  BATTLE_PAUSED = 'BattlePaused',
  BATTLE_RESUMED = 'BattleResumed',
  BATTLE_ENDED = 'BattleEnded',


  ACTION_QUEUED = 'ActionQueued',
  ACTION_EXECUTED = 'ActionExecuted',
  DAMAGE_CALCULATED = 'DamageCalculated',
  HEALING_CALCULATED = 'HealingCalculated',
  STATUS_EFFECT_APPLIED = 'StatusEffectApplied',
  STATUS_EFFECT_REMOVED = 'StatusEffectRemoved',
  CARD_HEALTH_CHANGED = 'CardHealthChanged',
  CARD_DEFEATED = 'CardDefeated',
  SKILL_COOLDOWN_UPDATED = 'SkillCooldownUpdated',
  AI_DECISION_MADE = 'AIDecisionMade',
  ANIMATION_STARTED = 'AnimationStarted',
  ANIMATION_COMPLETED = 'AnimationCompleted'
}

// ==================== 戰鬥管理器接口 ====================

/**
 * 戰鬥管理器接口
 */
export interface BattleManager {
  currentBattle: Battle | null;         // 當前戰鬥
  
  // 戰鬥控制方法
  initializeBattle(stageId: string, playerTeam: (Card | null)[][]): Promise<Battle>;
  startBattle(): void;
  pauseBattle(): void;
  resumeBattle(): void;
  endBattle(winner: 'player' | 'enemy'): void;
  
  // 戰鬥邏輯方法
  processActionQueue(): void;
  executeAction(action: BattleAction): void;
  calculateDamage(attacker: BattleCard, target: BattleCard, skillId?: string): DamageCalculation;
  calculateHealing(healer: BattleCard, target: BattleCard, skillId?: string): HealingCalculation;
  updateActionBars(deltaTime: number): void;
  checkVictoryConditions(): 'player' | 'enemy' | null;
  
  // AI相關方法
  makeAIDecision(aiCard: BattleCard): AIDecision;
  evaluateTargets(aiCard: BattleCard, actionType: BattleActionType): TargetEvaluation[];
  
  // 事件處理方法
  addEventListener(type: BattleEventType, callback: (event: BattleEvent) => void): void;
  removeEventListener(type: BattleEventType, callback: (event: BattleEvent) => void): void;
  dispatchEvent(event: BattleEvent): void;
}

/**
 * 戰鬥配置接口
 */
export interface BattleConfig {

  actionBarMax: number;                 // 行動條最大值
  criticalChance: number;               // 基礎暴擊率
  criticalMultiplier: number;           // 暴擊倍率
  speedToActionBarRatio: number;        // 速度到行動條的轉換比例
  statusEffectTickTiming: 'start' | 'end'; // 狀態效果觸發時機
  aiThinkTime: number;                  // AI思考時間(毫秒)
  animationSpeed: number;               // 動畫速度倍率
  enableBattleLog: boolean;             // 是否啟用戰鬥日誌
  maxLogEntries: number;                // 最大日誌條目數
}
