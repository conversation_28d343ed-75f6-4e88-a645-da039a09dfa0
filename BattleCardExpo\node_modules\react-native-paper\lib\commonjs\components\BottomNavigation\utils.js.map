{"version": 3, "names": ["_color", "_interopRequireDefault", "require", "e", "__esModule", "default", "getActiveTintColor", "activeColor", "defaultColor", "theme", "isV3", "colors", "onSecondaryContainer", "exports", "getInactiveTintColor", "inactiveColor", "onSurfaceVariant", "color", "alpha", "rgb", "string", "getLabelColor", "tintColor", "hasColor", "focused", "onSurface"], "sourceRoot": "../../../../src", "sources": ["components/BottomNavigation/utils.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA0B,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAUnB,MAAMG,kBAAkB,GAAGA,CAAC;EACjCC,WAAW;EACXC,YAAY;EACZC;AAGF,CAAC,KAAK;EACJ,IAAI,OAAOF,WAAW,KAAK,QAAQ,EAAE;IACnC,OAAOA,WAAW;EACpB;EAEA,IAAIE,KAAK,CAACC,IAAI,EAAE;IACd,OAAOD,KAAK,CAACE,MAAM,CAACC,oBAAoB;EAC1C;EAEA,OAAOJ,YAAY;AACrB,CAAC;AAACK,OAAA,CAAAP,kBAAA,GAAAA,kBAAA;AAEK,MAAMQ,oBAAoB,GAAGA,CAAC;EACnCC,aAAa;EACbP,YAAY;EACZC;AAGF,CAAC,KAAK;EACJ,IAAI,OAAOM,aAAa,KAAK,QAAQ,EAAE;IACrC,OAAOA,aAAa;EACtB;EAEA,IAAIN,KAAK,CAACC,IAAI,EAAE;IACd,OAAOD,KAAK,CAACE,MAAM,CAACK,gBAAgB;EACtC;EAEA,OAAO,IAAAC,cAAK,EAACT,YAAY,CAAC,CAACU,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AACtD,CAAC;AAACP,OAAA,CAAAC,oBAAA,GAAAA,oBAAA;AAEK,MAAMO,aAAa,GAAGA,CAAC;EAC5BC,SAAS;EACTC,QAAQ;EACRC,OAAO;EACPhB,YAAY;EACZC;AAKF,CAAC,KAAK;EACJ,IAAIc,QAAQ,EAAE;IACZ,OAAOD,SAAS;EAClB;EAEA,IAAIb,KAAK,CAACC,IAAI,EAAE;IACd,IAAIc,OAAO,EAAE;MACX,OAAOf,KAAK,CAACE,MAAM,CAACc,SAAS;IAC/B;IACA,OAAOhB,KAAK,CAACE,MAAM,CAACK,gBAAgB;EACtC;EAEA,OAAOR,YAAY;AACrB,CAAC;AAACK,OAAA,CAAAQ,aAAA,GAAAA,aAAA", "ignoreList": []}