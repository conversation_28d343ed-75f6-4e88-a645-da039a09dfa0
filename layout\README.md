# 🎮 PetingGame UI Layout 設計示例

這個資料夾包含了基於遊戲設計文檔的 UI 佈局示例，專為手機優化設計。

## 📱 設計文件列表

### 1. **card-ui.html** - 卡牌UI設計
- **功能**：展示不同種族卡牌的視覺設計
- **特色**：
  - 6種種族（人族、精靈、獸人、龍族、天使、惡魔）
  - 種族專屬色彩系統
  - 觸控友好設計（120x160px）
  - 互動式行動條動畫
  - 懸停和點擊效果
  - 響應式適配

### 2. **main-menu.html** - 主界面佈局
- **功能**：遊戲主界面，關卡選擇和隊伍狀態
- **特色**：
  - 手機直向佈局（9:16比例優化）
  - 狀態欄整合（電量、時間、資源）
  - 戰鬥預覽區域
  - 隊伍戰力顯示
  - FAB快速操作按鈕
  - 底部導航標籤

### 3. **battle-ui.html** - 戰鬥界面
- **功能**：自動戰鬥界面，實時戰鬥狀態顯示
- **特色**：
  - 敵方區域（30%）+ 玩家區域（40%）
  - 中間行動預告系統
  - 實時血量條和行動條
  - 戰鬥控制欄（暫停、速度、截圖）
  - 動態戰鬥動畫
  - 觸控反饋效果

### 4. **gacha-ui.html** - 抽卡界面
- **功能**：卡牌召喚系統，多種抽卡選項
- **特色**：
  - 主要抽卡選項（大按鈕設計）
  - 次要抽卡選項（十連、金幣、免費）
  - 抽卡結果彈窗動畫
  - 機率說明功能
  - 倒計時顯示
  - 漸變背景設計

## 🎨 設計原則

### 視覺設計
- **現代 Material Design**：圓角、陰影、動畫過渡
- **種族色彩編碼**：每個種族有專屬色彩主題
- **玻璃態效果**：backdrop-filter 模糊背景
- **漸變背景**：豐富的色彩層次

### 交互設計
- **觸控優化**：最小48dp觸控區域
- **即時反饋**：懸停、點擊、縮放動畫
- **狀態指示**：清晰的視覺狀態反饋
- **手勢支援**：拖拽、長按、雙擊

### 響應式設計
- **手機優先**：專為 5-7 吋螢幕優化
- **多解析度適配**：小、中、大螢幕適配
- **直向佈局**：主要針對直向使用模式
- **動態縮放**：根據螢幕大小調整元素

## 🛠️ 技術實現

### CSS特性
- **CSS Grid & Flexbox**：現代佈局技術
- **CSS變量**：統一的色彩和尺寸系統
- **CSS動畫**：流暢的過渡和關鍵幀動畫
- **媒體查詢**：響應式斷點

### JavaScript功能
- **事件處理**：點擊、懸停、拖拽事件
- **動態內容**：實時更新數據顯示
- **動畫控制**：JavaScript + CSS動畫結合
- **狀態管理**：簡單的狀態切換邏輯

## 📋 使用說明

1. **直接開啟**：用瀏覽器直接打開HTML文件
2. **開發者工具**：使用F12切換到手機模擬模式
3. **建議解析度**：375x812 (iPhone X) 或類似比例
4. **互動測試**：點擊各種按鈕和卡牌測試效果

## 🔄 Unity實現建議

將這些HTML/CSS設計轉換到Unity時：

1. **UI組件對應**：
   - HTML div → Unity UI Panel
   - CSS flexbox → Horizontal/Vertical Layout Group  
   - CSS animations → DOTween動畫

2. **響應式實現**：
   - CSS media queries → Unity Canvas Scaler
   - 動態尺寸調整 → Content Size Fitter

3. **交互實現**：
   - CSS hover → Unity EventTrigger
   - JavaScript events → Unity C# Event System

4. **視覺效果**：
   - CSS gradients → Unity Gradient
   - backdrop-filter → Unity Blur Effects

---

*這些佈局設計完全基於遊戲設計文檔，專注於手機觸控體驗和現代UI設計原則。*