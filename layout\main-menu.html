<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Main Menu Layout - PetingGame</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Times New Roman', serif;
            background: linear-gradient(135deg, #f7f3e9 0%, #e8dcc0 50%, #d4c5a0 100%);
            min-height: 100vh;
            color: #3c2415;
            overflow-x: hidden;
        }

        /* 手機直向佈局 */
        .mobile-container {
            max-width: 375px;
            margin: 0 auto;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 狀態欄 */
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 20px;
            background: rgba(139, 69, 19, 0.2);
            backdrop-filter: blur(10px);
            font-size: 14px;
            font-weight: bold;
            font-family: 'Times New Roman', serif;
            color: #8b4513;
            border-bottom: 2px dashed rgba(139, 69, 19, 0.3);
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .resource {
            display: flex;
            align-items: center;
            gap: 4px;
            background: #f4a261;
            color: #8b4513;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            border: 2px solid #8b4513;
            box-shadow: 2px 2px 0px rgba(139, 69, 19, 0.3);
            transform: rotate(-1deg);
            font-family: 'Times New Roman', serif;
        }

        /* 主要內容區域 */
        .main-content {
            flex: 1;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* 關卡標題區域 */
        .stage-header {
            text-align: center;
            margin-bottom: 10px;
        }

        .stage-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #8b4513;
            font-family: 'Times New Roman', serif;
            transform: rotate(-1deg);
            text-shadow: 2px 2px 0px rgba(139, 69, 19, 0.3);
            text-decoration: underline;
            text-decoration-color: rgba(139, 69, 19, 0.3);
        }

        .stage-progress {
            font-size: 16px;
            color: #8b4513;
            font-family: 'Times New Roman', serif;
            transform: rotate(1deg);
            font-weight: bold;
        }

        /* 戰鬥預覽區域 */
        .battle-preview {
            background: linear-gradient(145deg, #f9f7f4, #f0ede6);
            border: 3px solid #8b4513;
            border-radius: 16px;
            padding: 30px 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.3);
            position: relative;
            filter: drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.1));
        }

        .battle-preview:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 30%, rgba(139, 69, 19, 0.1) 2px, transparent 2px),
                radial-gradient(circle at 70% 60%, rgba(139, 69, 19, 0.05) 1px, transparent 1px),
                radial-gradient(circle at 40% 80%, rgba(139, 69, 19, 0.08) 1px, transparent 1px);
            background-size: 20px 20px, 15px 15px, 25px 25px;
            border-radius: 16px;
            z-index: 1;
        }

        .battle-preview:after {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            border: 2px dashed #8b4513;
            border-radius: 12px;
            opacity: 0.3;
            z-index: 1;
        }

        .preview-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            position: relative;
            z-index: 2;
        }

        .preview-content h3 {
            color: #8b4513;
            font-family: 'Times New Roman', serif;
            transform: rotate(-1deg);
            text-decoration: underline;
            text-decoration-color: rgba(139, 69, 19, 0.3);
        }

        .enemy-preview {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .mini-enemy {
            width: 40px;
            height: 40px;
            background: linear-gradient(145deg, #f9f7f4, #f0ede6);
            border: 3px solid #dc2626;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
            transform: rotate(-2deg);
            transition: all 0.3s ease;
        }

        .mini-enemy:hover {
            transform: translateY(-2px) rotate(0deg) scale(1.1);
            box-shadow: 0 6px 18px rgba(139, 69, 19, 0.4);
        }

        .start-battle-btn {
            background: #f4a261;
            color: #8b4513;
            border: 3px solid #8b4513;
            padding: 16px 40px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Times New Roman', serif;
            box-shadow: 0 6px 20px rgba(139, 69, 19, 0.3);
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
            transform: rotate(-2deg);
        }

        .start-battle-btn:hover {
            background: #f59e0b;
            transform: translateY(-2px) rotate(0deg) scale(1.05);
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.4);
            filter: drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.2));
        }

        .start-battle-btn:active {
            transform: translateY(0);
        }

        /* 隊伍狀態區域 */
        .team-status {
            background: linear-gradient(145deg, #f9f7f4, #f0ede6);
            border: 3px solid #8b4513;
            border-radius: 12px;
            padding: 15px;
            position: relative;
            box-shadow: 0 6px 20px rgba(139, 69, 19, 0.3);
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
        }

        .team-status:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 25% 25%, rgba(139, 69, 19, 0.08) 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, rgba(139, 69, 19, 0.06) 1px, transparent 1px);
            background-size: 15px 15px, 12px 12px;
            border-radius: 12px;
            z-index: 1;
        }

        .team-power {
            font-size: 16px;
            margin-bottom: 12px;
            font-weight: bold;
            color: #8b4513;
            font-family: 'Times New Roman', serif;
            transform: rotate(-1deg);
            position: relative;
            z-index: 2;
        }

        .team-preview {
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            z-index: 2;
        }

        .team-card {
            width: 35px;
            height: 45px;
            background: linear-gradient(145deg, #f9f7f4, #f0ede6);
            border: 2px solid #8b4513;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            position: relative;
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
            filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.1));
            transition: all 0.3s ease;
        }

        .team-card.dragon { 
            border-color: #dc2626;
            transform: rotate(-3deg);
        }
        .team-card.elf { 
            border-color: #228b22;
            transform: rotate(2deg);
        }
        .team-card.orc { 
            border-color: #f59e0b;
            transform: rotate(-1deg);
        }

        .team-card:hover {
            transform: translateY(-2px) rotate(0deg) scale(1.1);
            box-shadow: 0 6px 18px rgba(139, 69, 19, 0.4);
        }

        .team-count {
            margin-left: auto;
            font-size: 14px;
            font-weight: bold;
            color: #8b4513;
            font-family: 'Times New Roman', serif;
            transform: rotate(1deg);
        }

        /* FAB 快速操作按鈕 */
        .fab {
            position: absolute;
            bottom: 80px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: #f4a261;
            border: 3px solid #8b4513;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #8b4513;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Times New Roman', serif;
            box-shadow: 0 6px 20px rgba(139, 69, 19, 0.3);
            filter: drop-shadow(3px 3px 6px rgba(0, 0, 0, 0.2));
            transform: rotate(-5deg);
            z-index: 10;
        }

        .fab:hover {
            background: #f59e0b;
            transform: scale(1.1) rotate(0deg);
            box-shadow: 0 8px 30px rgba(139, 69, 19, 0.4);
            filter: drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.3));
        }

        /* 底部導航 */
        .bottom-nav {
            background: rgba(139, 69, 19, 0.3);
            backdrop-filter: blur(20px);
            padding: 12px 0;
            border-top: 3px dashed rgba(139, 69, 19, 0.5);
        }

        .nav-tabs {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-tab {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            border-radius: 12px;
            transition: all 0.2s ease;
            cursor: pointer;
            min-width: 48px;
            min-height: 48px;
            justify-content: center;
            color: #8b4513;
            font-family: 'Times New Roman', serif;
            border: 2px solid transparent;
        }

        .nav-tab:hover {
            background: #f4a261;
            border-color: #8b4513;
            transform: rotate(-1deg) scale(1.05);
            box-shadow: 2px 2px 0px rgba(139, 69, 19, 0.3);
        }

        .nav-tab.active {
            background: #f4a261;
            color: #8b4513;
            border-color: #8b4513;
            transform: rotate(1deg);
            box-shadow: 2px 2px 0px rgba(139, 69, 19, 0.3);
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: bold;
            font-family: 'Times New Roman', serif;
        }

        /* 響應式調整 */
        @media (max-width: 360px) {
            .mobile-container {
                max-width: 100%;
            }
            
            .main-content {
                padding: 15px;
            }
            
            .stage-title {
                font-size: 20px;
            }
            
            .start-battle-btn {
                padding: 14px 30px;
                font-size: 16px;
            }
        }

        /* 動畫效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px) rotate(-2deg); }
            to { opacity: 1; transform: translateY(0) rotate(0deg); }
        }

        @keyframes handDrawnBob {
            0%, 100% { transform: translateY(0) rotate(-1deg); }
            50% { transform: translateY(-2px) rotate(1deg); }
        }

        .main-content > * {
            animation: fadeIn 0.6s ease forwards;
        }

        .stage-title {
            animation: handDrawnBob 4s infinite ease-in-out;
        }

        .main-content > *:nth-child(2) { animation-delay: 0.1s; }
        .main-content > *:nth-child(3) { animation-delay: 0.2s; }
        .main-content > *:nth-child(4) { animation-delay: 0.3s; }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- 狀態欄 -->
        <div class="status-bar">
            <div class="status-left">
                <span>🔋 95%</span>
                <span>15:30</span>
            </div>
            <div class="status-right">
                <div class="resource">
                    <span>💎</span>
                    <span>1,250</span>
                </div>
                <div class="resource">
                    <span>💰</span>
                    <span>5,600</span>
                </div>
            </div>
        </div>

        <!-- 主要內容 -->
        <div class="main-content">
            <!-- 關卡標題 -->
            <div class="stage-header">
                <h1 class="stage-title">🏆 第5關 森林深處</h1>
                <div class="stage-progress">進度: 3/3 ⭐⭐⭐</div>
            </div>

            <!-- 戰鬥預覽區域 -->
            <div class="battle-preview">
                <div class="preview-content">
                    <h3 style="margin-bottom: 15px; opacity: 0.9;">即將面對的敵人</h3>
                    <div class="enemy-preview">
                        <div class="mini-enemy">🐺</div>
                        <div class="mini-enemy">🧌</div>
                        <div class="mini-enemy">🕷️</div>
                    </div>
                    <div style="opacity: 0.8; margin-bottom: 20px;">
                        敵方戰力: 980
                    </div>
                    <button class="start-battle-btn">
                        ▶️ 開始戰鬥
                    </button>
                </div>
            </div>

            <!-- 隊伍狀態 -->
            <div class="team-status">
                <div class="team-power">📊 隊伍戰力: 1,250</div>
                <div class="team-preview">
                    <span style="opacity: 0.9; margin-right: 8px;">👥</span>
                    <div class="team-card dragon">🔥</div>
                    <div class="team-card elf">🏹</div>
                    <div class="team-card orc">🪓</div>
                    <div class="team-count">3/6</div>
                </div>
            </div>
        </div>

        <!-- FAB 快速操作按鈕 -->
        <div class="fab" title="快速操作">
            🚀
        </div>

        <!-- 底部導航 -->
        <div class="bottom-nav">
            <div class="nav-tabs">
                <div class="nav-tab active">
                    <div class="nav-icon">⚔️</div>
                    <div class="nav-label">戰鬥</div>
                </div>
                <div class="nav-tab">
                    <div class="nav-icon">🎴</div>
                    <div class="nav-label">抽卡</div>
                </div>
                <div class="nav-tab">
                    <div class="nav-icon">👥</div>
                    <div class="nav-label">隊伍</div>
                </div>
                <div class="nav-tab">
                    <div class="nav-icon">📊</div>
                    <div class="nav-label">數據</div>
                </div>
                <div class="nav-tab">
                    <div class="nav-icon">⚙️</div>
                    <div class="nav-label">設定</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 底部導航交互
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 開始戰鬥按鈕效果
        document.querySelector('.start-battle-btn').addEventListener('click', function() {
            this.style.background = '#f59e0b';
            this.style.transform = 'rotate(0deg) scale(1.05)';
            this.textContent = '⚔️ 戰鬥中...';
            setTimeout(() => {
                this.style.background = '#f4a261';
                this.style.transform = 'rotate(-2deg)';
                this.textContent = '▶️ 開始戰鬥';
            }, 2000);
        });

        // FAB 按鈕效果
        document.querySelector('.fab').addEventListener('click', function() {
            this.style.transform = 'scale(1.2) rotate(180deg)';
            this.style.filter = 'drop-shadow(6px 6px 12px rgba(0, 0, 0, 0.3))';
            setTimeout(() => {
                this.style.transform = 'scale(1) rotate(-5deg)';
                this.style.filter = 'drop-shadow(3px 3px 6px rgba(0, 0, 0, 0.2))';
            }, 300);
        });
    </script>
</body>
</html>