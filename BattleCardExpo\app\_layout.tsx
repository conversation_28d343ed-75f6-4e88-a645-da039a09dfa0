import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';
import { ConfigProvider, useConfig } from '@/contexts/ConfigContext';
import LoadingScreen from '@/components/LoadingScreen';

function AppContent() {
  const { isLoading, isLoaded, error } = useConfig();
  const colorScheme = useColorScheme();

  // 顯示載入畫面直到配置載入完成
  if (isLoading || !isLoaded) {
    return <LoadingScreen error={error} />;
  }

  return (
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <Stack>
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style="auto" />
    </ThemeProvider>
  );
}

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <ConfigProvider>
      <AppContent />
    </ConfigProvider>
  );
}
