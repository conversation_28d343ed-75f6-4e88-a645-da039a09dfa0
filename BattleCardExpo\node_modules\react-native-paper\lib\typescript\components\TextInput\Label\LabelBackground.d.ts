import * as React from 'react';
import type { LabelBackgroundProps } from '../types';
declare const LabelBackground: ({ labeled, labelLayoutWidth, labelLayoutHeight, placeholderStyle, baseLabelTranslateX, topPosition, backgroundColor, roundness, labelStyle, maxFontSizeMultiplier, testID, }: LabelBackgroundProps) => (React.JSX.Element | null)[];
export default LabelBackground;
//# sourceMappingURL=LabelBackground.d.ts.map