{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_overlay", "_colors", "e", "__esModule", "default", "borderStyleProperties", "getAppbarBackgroundColor", "theme", "elevation", "customBackground", "elevated", "isV3", "dark", "isDarkTheme", "mode", "colors", "isAdaptiveMode", "overlay", "surface", "primary", "level2", "exports", "getAppbarColor", "color", "isDark", "white", "undefined", "black", "getAppbarBorders", "style", "borders", "property", "value", "DEFAULT_APPBAR_HEIGHT", "MD3_DEFAULT_APPBAR_HEIGHT", "modeAppbarHeight", "small", "medium", "large", "modeTextVariant", "filterAppbarActions", "children", "isLeading", "React", "Children", "toArray", "filter", "child", "isValidElement", "props", "renderAppbarContent", "shouldCenterC<PERSON>nt", "renderOnly", "renderExcept", "includes", "type", "displayName", "map", "i", "styles", "v3Spacing", "v2Spacing", "centerAlignedContent", "cloneElement", "StyleSheet", "create", "alignItems", "marginLeft"], "sourceRoot": "../../../../src", "sources": ["components/Appbar/utils.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,QAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AAA6D,SAAAD,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAW7D,MAAMG,qBAAqB,GAAG,CAC5B,cAAc,EACd,qBAAqB,EACrB,sBAAsB,EACtB,yBAAyB,EACzB,wBAAwB,CACzB;AAEM,MAAMC,wBAAwB,GAAGA,CACtCC,KAAoB,EACpBC,SAAiB,EACjBC,gBAA6B,EAC7BC,QAAkB,KACf;EACH,MAAM;IAAEC,IAAI;IAAEC,IAAI,EAAEC,WAAW;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGR,KAAK;EACvD,MAAMS,cAAc,GAAGF,IAAI,KAAK,UAAU;EAC1C,IAAIL,gBAAgB,EAAE;IACpB,OAAOA,gBAAgB;EACzB;EAEA,IAAI,CAACE,IAAI,EAAE;IACT,IAAIE,WAAW,IAAIG,cAAc,EAAE;MACjC,OAAO,IAAAC,gBAAO,EAACT,SAAS,EAAEO,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG,OAAO,CAAC;IAC5C;IAEA,OAAOH,MAAM,CAACI,OAAO;EACvB;EAEA,IAAIT,QAAQ,EAAE;IACZ,OAAOH,KAAK,CAACQ,MAAM,CAACP,SAAS,CAACY,MAAM;EACtC;EAEA,OAAOL,MAAM,CAACG,OAAO;AACvB,CAAC;AAACG,OAAA,CAAAf,wBAAA,GAAAA,wBAAA;AAEK,MAAMgB,cAAc,GAAGA,CAAC;EAC7BC,KAAK;EACLC,MAAM;EACNb;AAC6B,CAAC,KAAK;EACnC,IAAI,OAAOY,KAAK,KAAK,WAAW,EAAE;IAChC,OAAOA,KAAK;EACd;EAEA,IAAIC,MAAM,EAAE;IACV,OAAOC,aAAK;EACd;EAEA,IAAId,IAAI,EAAE;IACR,OAAOe,SAAS;EAClB;EAEA,OAAOC,aAAK;AACd,CAAC;AAACN,OAAA,CAAAC,cAAA,GAAAA,cAAA;AAEK,MAAMM,gBAAgB,GAC3BC,KAG0C,IACvC;EACH,MAAMC,OAA+B,GAAG,CAAC,CAAC;EAE1C,KAAK,MAAMC,QAAQ,IAAI1B,qBAAqB,EAAE;IAC5C,MAAM2B,KAAK,GAAGH,KAAK,CAACE,QAAQ,CAAuB;IACnD,IAAIC,KAAK,EAAE;MACTF,OAAO,CAACC,QAAQ,CAAC,GAAGC,KAAK;IAC3B;EACF;EAEA,OAAOF,OAAO;AAChB,CAAC;AAACT,OAAA,CAAAO,gBAAA,GAAAA,gBAAA;AAiBK,MAAMK,qBAAqB,GAAAZ,OAAA,CAAAY,qBAAA,GAAG,EAAE;AACvC,MAAMC,yBAAyB,GAAG,EAAE;AAE7B,MAAMC,gBAAgB,GAAAd,OAAA,CAAAc,gBAAA,GAAG;EAC9BC,KAAK,EAAEF,yBAAyB;EAChCG,MAAM,EAAE,GAAG;EACXC,KAAK,EAAE,GAAG;EACV,gBAAgB,EAAEJ;AACpB,CAAC;AAEM,MAAMK,eAAe,GAAAlB,OAAA,CAAAkB,eAAA,GAAG;EAC7BH,KAAK,EAAE,YAAY;EACnBC,MAAM,EAAE,eAAe;EACvBC,KAAK,EAAE,gBAAgB;EACvB,gBAAgB,EAAE;AACpB,CAAU;AAEH,MAAME,mBAAmB,GAAGA,CACjCC,QAAyB,EACzBC,SAAS,GAAG,KAAK,KACd;EACH,OAAOC,cAAK,CAACC,QAAQ,CAACC,OAAO,CAACJ,QAAQ,CAAC,CAACK,MAAM,CAAEC,KAAK,IAAK;IACxD,IAAI,eAACJ,cAAK,CAACK,cAAc,CAAmBD,KAAK,CAAC,EAAE,OAAO,KAAK;IAChE,OAAOL,SAAS,GAAGK,KAAK,CAACE,KAAK,CAACP,SAAS,GAAG,CAACK,KAAK,CAACE,KAAK,CAACP,SAAS;EACnE,CAAC,CAAC;AACJ,CAAC;AAACrB,OAAA,CAAAmB,mBAAA,GAAAA,mBAAA;AAEK,MAAMU,mBAAmB,GAAGA,CAAC;EAClCT,QAAQ;EACRjB,MAAM;EACN2B,mBAAmB,GAAG,KAAK;EAC3BxC,IAAI;EACJyC,UAAU;EACVC,YAAY;EACZvC,IAAI,GAAG,OAAO;EACdP;AACwB,CAAC,KAAK;EAC9B,OAAOoC,cAAK,CAACC,QAAQ,CAACC,OAAO,CAACJ,QAA+C,CAAC,CAC3EK,MAAM,CAAEC,KAAK,IAAKA,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,SAAS,CAAC,CAC9DD,MAAM,CAAEC,KAAK;EACZ;EACAM,YAAY,GAAG,CAACA,YAAY,CAACC,QAAQ,CAACP,KAAK,CAACQ,IAAI,CAACC,WAAW,CAAC,GAAGT,KAClE,CAAC,CACAD,MAAM,CAAEC,KAAK;EACZ;EACAK,UAAU,GAAGA,UAAU,CAACE,QAAQ,CAACP,KAAK,CAACQ,IAAI,CAACC,WAAW,CAAC,GAAGT,KAC7D,CAAC,CACAU,GAAG,CAAC,CAACV,KAAK,EAAEW,CAAC,KAAK;IACjB,IACE,eAACf,cAAK,CAACK,cAAc,CAAmBD,KAAK,CAAC,IAC9C,CAAC,CACC,gBAAgB,EAChB,eAAe,EACf,mBAAmB,EACnB,SAAS,CACV,CAACO,QAAQ;IACR;IACAP,KAAK,CAACQ,IAAI,CAACC,WACb,CAAC,EACD;MACA,OAAOT,KAAK;IACd;IAEA,MAAME,KAKL,GAAG;MACF1C,KAAK;MACLgB,KAAK,EAAED,cAAc,CAAC;QAAEC,KAAK,EAAEwB,KAAK,CAACE,KAAK,CAAC1B,KAAK;QAAEC,MAAM;QAAEb;MAAK,CAAC;IAClE,CAAC;;IAED;IACA,IAAIoC,KAAK,CAACQ,IAAI,CAACC,WAAW,KAAK,gBAAgB,EAAE;MAC/CP,KAAK,CAACnC,IAAI,GAAGA,IAAI;MACjBmC,KAAK,CAACpB,KAAK,GAAG,CACZlB,IAAI,GACA+C,CAAC,KAAK,CAAC,IAAI,CAACP,mBAAmB,IAAIQ,MAAM,CAACC,SAAS,GACnDF,CAAC,KAAK,CAAC,IAAIC,MAAM,CAACE,SAAS,EAC/BV,mBAAmB,IAAIQ,MAAM,CAACG,oBAAoB,EAClDf,KAAK,CAACE,KAAK,CAACpB,KAAK,CAClB;MACDoB,KAAK,CAAC1B,KAAK;IACb;IACA,oBAAOoB,cAAK,CAACoB,YAAY,CAAChB,KAAK,EAAEE,KAAK,CAAC;EACzC,CAAC,CAAC;AACN,CAAC;AAAC5B,OAAA,CAAA6B,mBAAA,GAAAA,mBAAA;AAEF,MAAMS,MAAM,GAAGK,uBAAU,CAACC,MAAM,CAAC;EAC/BH,oBAAoB,EAAE;IACpBI,UAAU,EAAE;EACd,CAAC;EACDL,SAAS,EAAE;IACTM,UAAU,EAAE;EACd,CAAC;EACDP,SAAS,EAAE;IACTO,UAAU,EAAE;EACd;AACF,CAAC,CAAC", "ignoreList": []}