# 🃏 PetingGame 自動戰鬥卡牌遊戲設計文檔

## 目錄
1. [遊戲概述](#1-遊戲概述)
2. [核心遊戲循環](#2-核心遊戲循環)
3. [手機優化界面設計](#3-手機優化界面設計)
4. [卡牌系統設計](#4-卡牌系統設計)
5. [抽卡系統設計](#5-抽卡系統設計)
6. [自動戰鬥機制](#6-自動戰鬥機制)
7. [配置系統設計](#7-配置系統設計)
8. [標籤系統設計](#8-標籤系統設計)
9. [技術架構](#9-技術架構)
10. [實作優先級](#10-實作優先級)

---

## 1. 遊戲概述

### 1.1 遊戲類型與視覺風格
**PetingGame** 是一款自動戰鬥策略卡牌收集遊戲，採用現代簡潔的UI設計，專注於卡牌收集、構築和觀看精彩的自動戰鬥。

### 1.2 視覺設計原則
- **手機優先設計**：專為5-7吋螢幕優化，支援直向顯示為主
- **React Native UI系統**：基於React Native和Expo構建，支援iOS/Android原生體驗
- **本地運行**：完全離線運行，無需網路連接
- **觸控友好**：所有UI元件針對手指觸控進行優化，最小觸控區域44pt
- **清晰的視覺層次**：突出重要信息，弱化次要元素
- **現代設計語言**：採用現代移動端設計，圓角、陰影、流暢動畫過渡

### 1.3 核心設計理念
- **全自動戰鬥**：戰鬥完全自動進行，玩家專注於策略構築
- **速度驅動**：基於卡牌速度屬性的行動順序系統
- **策略深度**：通過卡牌組合和陣容配置實現策略性
- **觀賞性**：流暢精彩的自動戰鬥演出

---

## 2. 核心遊戲循環

### 2.1 主要遊戲循環
```
觀看自動戰鬥 → 獲得卡牌獎勵 → 構築優化牌組 → 升級強化卡牌 → 自動進入下一戰鬥 → 觀看自動戰鬥
```

### 2.2 戰鬥進行循環
```
初始化戰場 → 自動開始戰鬥 → 計算行動順序 → 執行卡牌行動 → 更新戰場狀態 → 檢查勝負條件 → 自動進入下一戰鬥或結束
```

### 2.3 自動戰鬥特性
- **無預覽階段**：玩家點擊進入戰鬥後，戰鬥立即自動開始
- **完全自動化**：戰鬥過程中無需玩家干預，所有行動由AI自動執行
- **觀賞體驗**：玩家專注於觀看戰鬥過程和結果
- **可控制選項**：玩家可以暫停/繼續、調整戰鬥速度（1x, 2x, 4x）

---

## 3. 手機優化界面設計

### 3.1 主界面佈局（直向顯示）
```
React Native 手機直向佈局 (9:16比例優化):
┌─────────────────────────────────┐
│ 🔋95% 15:30      💎1,250 💰5,600│ ← StatusBar + SafeArea整合
├─────────────────────────────────┤
│                                 │
│         🏆 第5關 森林深處        │ ← 當前關卡大標題
│         進度: 3/3 ⭐⭐⭐         │ ← 進度指示器
│                                 │
│  ┌─────────────────────────────┐ │
│  │                             │ │
│  │       戰鬥預覽區域           │ │ ← 主要內容區域（40%螢幕）
│  │     [▶️ 進入戰鬥]           │ │   ScrollView + Animated.View
│  │     （點擊後自動開始）        │ │   
│  │                             │ │
│  └─────────────────────────────┘ │
│                                 │
│  📊 隊伍戰力: 1,250             │ ← 隊伍狀態
│  👥 [火龍][水法][地騎] 3/6      │ ← 當前隊伍預覽（FlatList）
│                                 │
├─────────────────────────────────┤
│ 🚀                              │ ← FAB (react-native-paper)
├─────────────────────────────────┤
│[⚔️][🎴][👥][📊][⚙️]            │ ← Tab Navigator (底部導航)
└─────────────────────────────────┘
```

### 3.2 戰鬥界面佈局（直向顯示）
```
React Native 戰鬥界面優化佈局:
┌─────────────────────────────────┐
│ ⚔️ Stage 1-5  ⏸️  🔊 ⚙️       │ ← Header (react-navigation)
├─────────────────────────────────┤
│       🔴 ENEMY (3/3) ❤️450      │ ← 敵方狀態
│ ┌─────────────────────────────┐ │
│ │  [敵人1] [敵人2] [敵人3]     │ │ ← 敵方卡牌區（30%）
│ │  ▼▼▼    ▼▼▼    ▼▼▼        │ │   FlatList horizontal
│ └─────────────────────────────┘ │
├─────────────────────────────────┤
│ ⚡ 下一個: 火龍戰士 (2秒)       │ ← 行動預告系統
│ 📊 [你][敵][你][敵][你]         │ ← 簡化行動順序條 (Animated)
├─────────────────────────────────┤
│       🔵 YOUR TEAM (3/3) ❤️850  │ ← 己方狀態
│ ┌─────────────────────────────┐ │
│ │    [玩家1]  [玩家2]  [玩家3] │ │ ← 己方卡牌區（40%）
│ │    ████     ████     ████    │ │   FlatList + TouchableOpacity
│ │   Lv.5 ⚔️120 Lv.3 💧85 Lv.4│ │
│ └─────────────────────────────┘ │
├─────────────────────────────────┤
│[📊詳情] [⏩2x] [⏸️暫停] [📷]   │ ← 戰鬥控制欄 (Button組件)
└─────────────────────────────────┘
```

### 3.3 卡牌顯示設計（手機優化）
```
React Native 單張卡牌設計 (適合手機觸控):
┌─────────────────┐  寬120dp x 高160dp
│ Lv.5      ❤️85   │  ← 關鍵信息層 (Text組件)
│                 │
│   🔥火龍戰士     │  ← 名稱層（18sp字體）
│                 │
│ ⚔️120    ⚡15    │  ← 屬性層（16sp字體）
│ ██████████░░░░   │  ← 行動條（ProgressBar/Animated.View）
│ 🔥龍族 ⭐⭐⭐⭐   │  ← 種族+稀有度標籤 (Badge組件)
└─────────────────┘

觸控反應 (Gesture Handler):
- onPress: 選中高亮（Animated.spring縮放）
- onLongPress: 顯示詳細信息Modal
- PanGestureHandler: 卡牌位置調整
- 雙擊: 快速加入/移除隊伍 (onPress計數)
```

### 3.4 導航系統設計

#### 3.4.1 底部標籤導航 (@react-navigation/bottom-tabs)
- **⚔️戰鬥**：主要遊戲界面，關卡選擇和戰鬥
- **🎴抽卡**：卡牌召喚和抽卡池
- **👥隊伍**：隊伍編成和卡牌管理
- **📊數據**：統計信息和成就系統
- **⚙️設定**：遊戲設置和選項

#### 3.4.2 懸浮操作按鈕（FAB - react-native-paper）
```
主按鈕狀態:
戰鬥中: [⏸️] 暫停/繼續
卡牌頁: [🎴] 快速抽卡
隊伍頁: [💪] 一鍵優化隊伍
設置頁: [💾] 保存設定

展開選項: FAB.Group 組件展開2-3個次要選項
- 圓形排列，背景半透明模糊
- 60dp主按鈕，48dp次要按鈕
```

### 3.5 響應式適配 (react-native-super-grid + Dimensions)
- **小螢幕（<5.5吋）**：緊湊佈局，減少邊距
- **中等螢幕（5.5-6.5吋）**：標準佈局，平衡的間距
- **大螢幕（>6.5吋）**：放大元素，增加內容密度
- **橫向模式**：自動切換到並排佈局，左右分區 (useDeviceOrientation)

---

## 4. 卡牌系統設計

### 4.1 卡牌基礎屬性
```
核心屬性:
- Name: 卡牌名稱
- Attack: 攻擊力（紅色數字顯示）
- Health: 生命值（綠色血條顯示）
- Speed: 行動速度（決定行動順序）
- Race: 卡牌種族（彩色標籤）

擴展屬性:
- Rarity: 稀有度（1-5星，背景色彩）
- Tags: 標籤列表（小型彩色標籤）
- Skills: 技能列表（技能圖標）
- Level: 卡牌等級（左上角徽章）
```

### 4.2 種族系統
```
人族（Human）：平衡屬性，藍色主題
精靈族（Elf）：高速度魔攻，綠色主題  
獸人族（Orc）：高攻擊生命，橙色主題
龍族（Dragon）：極高屬性，紅色主題
天使族（Angel）：治療輔助，紫色主題
惡魔族（Demon）：詛咒減益，暗紅主題

種族相剋:
- 相剋關係影響傷害計算
- 視覺上用顏色和圖標提示
- 戰鬥中顯示剋制效果
```

### 4.3 卡牌交互設計
```
交互方式 (React Native Gesture Handler):
- TouchableOpacity: 卡牌邊框高亮
- onLongPress: 彈出詳情Modal
- PanGestureHandler: 調整卡牌位置
- 雙擊快選: 快速加入隊伍 (onPress計數)
- 手勢操作: 左右滑動切換頁面 (Swiper)

視覺反饋 (Animated API):
- 觸控縮放: Animated.spring 按下時微縮放
- 選中效果: 發光邊框動畫 (Animated.loop)
- 行動提示: 脈衝光效 (Animated.sequence)
- 傷害效果: Haptics.impact + 紅閃動畫
```

---

## 5. 抽卡系統設計

### 5.1 抽卡界面（手機優化）
```
React Native 抽卡界面佈局:
┌─────────────────────────────────┐
│ 🎴 卡牌召喚              [✕]   │ ← Header (react-navigation)
├─────────────────────────────────┤
│ 💎 鑽石: 1,250  💰 金幣: 5,600  │ ← 資源顯示 (StatusBar組件)
├─────────────────────────────────┤
│                                 │
│  ┌─────────────┐               │
│  │  單次召喚   │  ← 主要選項    │
│  │   💎 100    │  (Button組件)  │
│  │ [立即召喚]  │               │
│  └─────────────┘               │
│                                 │
│  ┌─────┐ ┌─────┐ ┌─────┐       │
│  │十連抽││金幣抽││免費抽│       │ ← 次要選項
│  │💎900 ││💰1000││⏰23h ││      │   (TouchableOpacity)
│  │[召喚]││[召喚]││[召喚]│      │
│  └─────┘ └─────┘ └─────┘       │
│                                 │
│         📊 機率說明              │ ← 機率信息 (Collapsible)
└─────────────────────────────────┘
```

### 5.2 抽卡結果展示
```
React Native 結果展示動畫 (Lottie + Animated):
1. 卡包開啟動畫（1秒）- Lottie動畫
2. 光效爆發（0.5秒）- Animated.timing
3. 卡牌翻轉顯示（1秒）- Animated.spring + rotateY
4. 稀有度特效（根據稀有度）- 條件渲染Lottie
5. 信息顯示（名稱、屬性、技能）- Animated.stagger

稀有度效果 (Lottie Files):
⭐ 普通: 白光，無特效
⭐⭐ 稀有: 藍光，小星星
⭐⭐⭐ 史詩: 紫光，光柱
⭐⭐⭐⭐ 傳說: 金光，彩虹
⭐⭐⭐⭐⭐ 神話: 七彩光，粒子爆發
```

### 5.3 機率與保底
```
基礎機率:
普通(⭐): 60% | 稀有(⭐⭐): 25% | 史詩(⭐⭐⭐): 12%
傳說(⭐⭐⭐⭐): 2.5% | 神話(⭐⭐⭐⭐⭐): 0.5%

保底機制:
- 10連保底: 至少1張稀有
- 50抽保底: 至少1張史詩  
- 200抽保底: 至少1張傳說
- 每日免費: 1次免費單抽
```

---

## 6. 自動戰鬥機制

### 6.1 戰鬥流程
```
戰鬥初始化:
1. 載入雙方隊伍配置
2. 計算初始屬性
3. 重置行動條為0
4. 立即自動開始戰鬥循環

戰鬥循環:
1. 更新行動條 (每幀+速度/10)
2. 處理行動條滿的卡牌
3. 執行AI決策和行動
4. 更新視覺效果
5. 檢查勝負條件
6. 循環直到戰鬥結束

戰鬥控制:
- 玩家可暫停/繼續戰鬥觀賞
- 可調整戰鬥速度 (1x, 2x, 4x)
- 可隨時退出戰鬥
```

### 6.2 AI行為模式
```
攻擊型AI:
優先級: 技能攻擊 > 普通攻擊 > 防禦
目標: 生命最低的敵人

防禦型AI:  
優先級: 治療 > 防禦技能 > 攻擊
目標: 生命最低的友軍

平衡型AI:
優先級: 根據戰況動態調整
目標: 智能選擇最優目標
```

### 6.3 戰鬥視覺效果與用戶體驗
```
React Native 動作效果 (Animated + Lottie):
- 攻擊: 卡牌前衝動畫 + 衝擊波 (Animated.sequence)
- 技能: 特殊光效 + 粒子系統 (Lottie)
- 受傷: 卡牌震動 + 血量條減少 (Animated.spring + ProgressBar)
- 死亡: 淡出動畫 + 光散效果 (Animated.timing + opacity)

UI反饋:
- 傷害數字: 彈出式傷害顯示 (Animated.timing + translateY)
- 狀態圖標: 增益/減益提示 (Badge組件)
- 行動提示: 下一個行動角色高亮 (Animated.loop)
- 戰鬥速度: 1x, 2x, 4x速度選項 (SegmentedControl)
- 暫停狀態: 明顯的暫停提示文字

自動戰鬥特色:
- 無需預覽確認，進入即開始
- 專注觀賞體驗，流暢的戰鬥演出
- 智能AI決策，展現策略深度
- 可控制的觀賞節奏
```

---

## 7. 配置系統設計

### 7.1 CSV配置文件結構
```
配置文件列表:
- CardConfig.csv: 卡牌基礎數據
- SkillConfig.csv: 技能效果配置
- GachaConfig.csv: 抽卡池配置
- StageConfig.csv: 關卡敵人配置
- DropConfig.csv: 掉落獎勵配置

存放位置: assets/config/ (Expo Asset)
編輯方式: Excel編輯後導出CSV
載入時機: 遊戲啟動時一次性載入 (expo-file-system)
```

### 7.2 本地數據管理
```
玩家數據 (AsyncStorage + expo-secure-store):
- 等級經驗: level, experience
- 遊戲貨幣: diamonds, gold
- 卡牌收藏: owned_cards
- 隊伍配置: current_deck
- 遊戲進度: current_stage
- 設定選項: settings

保存時機:
- 獲得新卡牌時
- 隊伍配置變更時
- 關卡完成時
- 設定修改時
```

---

## 8. 標籤系統設計

### 8.1 標籤分類
```
戰鬥標籤:
- Warrior(戰士), Mage(法師), Archer(射手)
- Tank(坦克), Healer(治療), Support(輔助)

元素標籤:  
- Fire(火), Water(水), Earth(土), Air(風)
- Light(光), Dark(暗)

特殊標籤:
- Elite(精英), Boss(首領), Minion(小兵)
- Flying(飛行), Undead(亡靈), Beast(野獸)
```

### 8.2 標籤視覺設計
```
標籤顯示:
- 圓角矩形背景
- 對應元素顏色
- 12px小字體
- 自動換行排列

顏色編碼:
火焰🔥: 紅色 | 水流💧: 藍色 | 大地🌍: 棕色
戰士⚔️: 橙色 | 法師🔮: 紫色 | 射手🏹: 綠色
```

---

## 9. 技術架構

### 9.1 React Native + Expo 開發規範
```
技術選型:
- Expo SDK 50+
- React Native 0.73+
- TypeScript 5.0+
- React Navigation 6
- React Native Paper (Material Design)
- Lottie React Native (動畫)
- React Native Gesture Handler
- AsyncStorage + Expo SecureStore

專案結構:
src/
├── components/     # 可重用組件
├── screens/        # 頁面組件
├── navigation/     # 導航配置
├── services/       # 業務邏輯服務
├── types/          # TypeScript型別定義
├── utils/          # 工具函數
├── assets/         # 靜態資源
└── config/         # 配置文件
```

### 9.2 性能優化策略
```
記憶體管理:
- FlatList虛擬化長列表
- 圖片優化和快取 (expo-image)
- 音頻壓縮和動態載入 (expo-av)

渲染優化:
- React.memo 防止不必要重渲染
- useMemo/useCallback 優化計算
- Animated.createAnimatedComponent 原生動畫

幀率優化:
- InteractionManager 延遲非關鍵任務
- requestAnimationFrame 控制更新頻率
- 背景任務使用 setTimeout 分片處理
```

---

## 10. 實作優先級

### 10.1 核心功能（第一階段）
1. **基礎UI框架**：React Navigation + Tab Navigator
2. **卡牌系統**：卡牌組件、屬性計算、TypeScript型別
3. **戰鬥系統**：自動戰鬥邏輯、Animated API
4. **配置系統**：CSV載入 (expo-file-system) 和 AsyncStorage

### 10.2 擴展功能（第二階段）
1. **抽卡系統**：召喚界面和機率計算、Lottie動畫
2. **隊伍管理**：編隊界面和隊伍保存、Gesture Handler
3. **關卡系統**：關卡選擇和進度管理
4. **音效系統**：背景音樂和音效反饋 (expo-av)

### 10.3 優化功能（第三階段）
1. **視覺特效**：戰鬥動畫和Lottie粒子效果
2. **數據統計**：成就系統和統計界面
3. **設定選項**：遊戲設定和個性化 (expo-settings)
4. **性能優化**：FlatList虛擬化、圖片快取優化

---

*最後更新: 2025年8月8日 - 已更新為 React Native + Expo 實作架構*