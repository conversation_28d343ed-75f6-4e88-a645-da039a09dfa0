/**
 * 🏠 主入口頁面組件
 * 
 * 基於 layout/main-menu.html 設計，包含戰鬥預覽和自動進入戰鬥功能
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  StatusBar,
  SafeAreaView,
  Alert
} from 'react-native';

import { useConfig } from '../contexts/ConfigContext';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface MainEntryProps {
  onStartBattle?: () => void;
  onNavigateToTab?: (tab: string) => void;
}

export default function MainEntry({ onStartBattle, onNavigateToTab }: MainEntryProps) {
  const { configManager, isLoaded, stats } = useConfig();
  const [currentStage, setCurrentStage] = useState('stage_1');
  const [playerResources, setPlayerResources] = useState({
    diamonds: 1250,
    gold: 5600,
    level: 5,
    experience: 750
  });

  // 獲取當前關卡配置
  const stageConfig = isLoaded ? configManager.getStageConfig(currentStage) : null;
  
  // 模擬隊伍數據
  const [teamData] = useState({
    power: 1250,
    cards: [
      { id: 'fire_dragon', emoji: '🔥', level: 5, race: 'dragon' },
      { id: 'water_mage', emoji: '💧', level: 3, race: 'elf' },
      { id: 'earth_knight', emoji: '🌍', level: 4, race: 'orc' }
    ],
    maxSize: 6
  });

  // 模擬敵人預覽數據
  const [enemyPreview] = useState({
    enemies: ['🐺', '🧌', '🕷️'],
    power: 980
  });

  const handleStartBattle = () => {
    if (!isLoaded) {
      Alert.alert('載入中', '配置尚未載入完成，請稍候...');
      return;
    }

    if (!stageConfig) {
      Alert.alert('錯誤', '無法找到關卡配置');
      return;
    }

    console.log('🚀 開始戰鬥:', stageConfig.name);
    
    // 這裡可以添加戰鬥開始的邏輯
    if (onStartBattle) {
      onStartBattle();
    } else {
      // 模擬自動進入戰鬥
      Alert.alert(
        '戰鬥開始!',
        `即將進入 ${stageConfig.name}\n推薦戰力: ${stageConfig.recommendedPower}`,
        [
          { text: '取消', style: 'cancel' },
          { text: '開始', onPress: () => console.log('進入戰鬥場景') }
        ]
      );
    }
  };

  const handleNavigation = (tab: string) => {
    if (onNavigateToTab) {
      onNavigateToTab(tab);
    } else {
      console.log('導航到:', tab);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#f7f3e9" />
      
      {/* 狀態欄 */}
      <View style={styles.statusBar}>
        <View style={styles.statusLeft}>
          <Text style={styles.statusText}>🔋 95%</Text>
          <Text style={styles.statusText}>15:30</Text>
        </View>
        <View style={styles.statusRight}>
          <View style={styles.resource}>
            <Text style={styles.resourceText}>💎 {playerResources.diamonds.toLocaleString()}</Text>
          </View>
          <View style={styles.resource}>
            <Text style={styles.resourceText}>💰 {playerResources.gold.toLocaleString()}</Text>
          </View>
        </View>
      </View>

      <ScrollView style={styles.mainContent} showsVerticalScrollIndicator={false}>
        {/* 關卡標題區域 */}
        <View style={styles.stageHeader}>
          <Text style={styles.stageTitle}>
            🏆 {stageConfig?.name || '載入中...'}
          </Text>
          <Text style={styles.stageProgress}>
            進度: 3/3 ⭐⭐⭐
          </Text>
        </View>

        {/* 戰鬥預覽區域 */}
        <View style={styles.battlePreview}>
          <View style={styles.previewContent}>
            <Text style={styles.previewTitle}>即將面對的敵人</Text>
            
            <View style={styles.enemyPreview}>
              {enemyPreview.enemies.map((enemy, index) => (
                <View key={index} style={styles.miniEnemy}>
                  <Text style={styles.enemyEmoji}>{enemy}</Text>
                </View>
              ))}
            </View>
            
            <Text style={styles.enemyPower}>
              敵方戰力: {enemyPreview.power}
            </Text>
            
            <TouchableOpacity 
              style={styles.startBattleBtn}
              onPress={handleStartBattle}
              activeOpacity={0.8}
            >
              <Text style={styles.startBattleText}>▶️ 開始戰鬥</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 隊伍狀態區域 */}
        <View style={styles.teamStatus}>
          <Text style={styles.teamPower}>
            📊 隊伍戰力: {teamData.power.toLocaleString()}
          </Text>
          
          <View style={styles.teamPreview}>
            <Text style={styles.teamIcon}>👥</Text>
            {teamData.cards.map((card, index) => {
              const raceStyle = card.race === 'dragon' ? styles.dragon :
                               card.race === 'elf' ? styles.elf :
                               card.race === 'orc' ? styles.orc : {};

              return (
                <View key={card.id} style={[styles.teamCard, raceStyle]}>
                  <Text style={styles.cardEmoji}>{card.emoji}</Text>
                </View>
              );
            })}
            <Text style={styles.teamCount}>
              {teamData.cards.length}/{teamData.maxSize}
            </Text>
          </View>
        </View>

        {/* 配置載入狀態 */}
        {isLoaded && (
          <View style={styles.configStatus}>
            <Text style={styles.configText}>
              ✅ 配置已載入: {stats.cards} 張卡牌, {stats.stages} 個關卡
            </Text>
          </View>
        )}
      </ScrollView>

      {/* FAB 快速操作按鈕 */}
      <TouchableOpacity style={styles.fab} activeOpacity={0.8}>
        <Text style={styles.fabIcon}>🚀</Text>
      </TouchableOpacity>

      {/* 底部導航 */}
      <View style={styles.bottomNav}>
        <View style={styles.navTabs}>
          {[
            { icon: '⚔️', label: '戰鬥', key: 'battle', active: true },
            { icon: '🎴', label: '抽卡', key: 'gacha' },
            { icon: '👥', label: '隊伍', key: 'team' },
            { icon: '📊', label: '數據', key: 'stats' },
            { icon: '⚙️', label: '設定', key: 'settings' }
          ].map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={[styles.navTab, tab.active && styles.navTabActive]}
              onPress={() => handleNavigation(tab.key)}
              activeOpacity={0.7}
            >
              <Text style={styles.navIcon}>{tab.icon}</Text>
              <Text style={styles.navLabel}>{tab.label}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f7f3e9',
  },
  statusBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 8,
    backgroundColor: 'rgba(139, 69, 19, 0.2)',
    borderBottomWidth: 2,
    borderBottomColor: 'rgba(139, 69, 19, 0.3)',
    borderStyle: 'dashed',
  },
  statusLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#8b4513',
    fontFamily: 'Times New Roman',
  },
  resource: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f4a261',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#8b4513',
    transform: [{ rotate: '-1deg' }],
  },
  resourceText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#8b4513',
    fontFamily: 'Times New Roman',
  },
  mainContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  stageHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  stageTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#8b4513',
    fontFamily: 'Times New Roman',
    transform: [{ rotate: '-1deg' }],
    textDecorationLine: 'underline',
    textDecorationColor: 'rgba(139, 69, 19, 0.3)',
    marginBottom: 8,
  },
  stageProgress: {
    fontSize: 16,
    color: '#8b4513',
    fontFamily: 'Times New Roman',
    transform: [{ rotate: '1deg' }],
    fontWeight: 'bold',
  },
  battlePreview: {
    backgroundColor: '#f9f7f4',
    borderWidth: 3,
    borderColor: '#8b4513',
    borderRadius: 16,
    padding: 30,
    marginBottom: 20,
    shadowColor: '#8b4513',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 25,
    elevation: 8,
  },
  previewContent: {
    alignItems: 'center',
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#8b4513',
    fontFamily: 'Times New Roman',
    transform: [{ rotate: '-1deg' }],
    textDecorationLine: 'underline',
    textDecorationColor: 'rgba(139, 69, 19, 0.3)',
    marginBottom: 15,
  },
  enemyPreview: {
    flexDirection: 'row',
    gap: 10,
    marginBottom: 20,
  },
  miniEnemy: {
    width: 40,
    height: 40,
    backgroundColor: '#f9f7f4',
    borderWidth: 3,
    borderColor: '#dc2626',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    transform: [{ rotate: '-2deg' }],
    shadowColor: '#8b4513',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 4,
  },
  enemyEmoji: {
    fontSize: 16,
  },
  enemyPower: {
    fontSize: 16,
    color: '#8b4513',
    fontFamily: 'Times New Roman',
    marginBottom: 20,
    opacity: 0.8,
  },
  startBattleBtn: {
    backgroundColor: '#f4a261',
    borderWidth: 3,
    borderColor: '#8b4513',
    paddingHorizontal: 40,
    paddingVertical: 16,
    borderRadius: 25,
    transform: [{ rotate: '-2deg' }],
    shadowColor: '#8b4513',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 6,
  },
  startBattleText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#8b4513',
    fontFamily: 'Times New Roman',
  },
  teamStatus: {
    backgroundColor: '#f9f7f4',
    borderWidth: 3,
    borderColor: '#8b4513',
    borderRadius: 12,
    padding: 15,
    marginBottom: 20,
    shadowColor: '#8b4513',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 6,
  },
  teamPower: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#8b4513',
    fontFamily: 'Times New Roman',
    transform: [{ rotate: '-1deg' }],
    marginBottom: 12,
  },
  teamPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  teamIcon: {
    fontSize: 16,
    opacity: 0.9,
    marginRight: 8,
  },
  teamCard: {
    width: 35,
    height: 45,
    backgroundColor: '#f9f7f4',
    borderWidth: 2,
    borderColor: '#8b4513',
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#8b4513',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 4,
  },
  dragon: {
    borderColor: '#dc2626',
    transform: [{ rotate: '-3deg' }],
  },
  elf: {
    borderColor: '#228b22',
    transform: [{ rotate: '2deg' }],
  },
  orc: {
    borderColor: '#f59e0b',
    transform: [{ rotate: '-1deg' }],
  },
  cardEmoji: {
    fontSize: 12,
  },
  teamCount: {
    marginLeft: 'auto',
    fontSize: 14,
    fontWeight: 'bold',
    color: '#8b4513',
    fontFamily: 'Times New Roman',
    transform: [{ rotate: '1deg' }],
  },
  configStatus: {
    backgroundColor: 'rgba(139, 69, 19, 0.1)',
    padding: 10,
    borderRadius: 8,
    marginBottom: 20,
  },
  configText: {
    fontSize: 12,
    color: '#8b4513',
    textAlign: 'center',
    fontFamily: 'Times New Roman',
  },
  fab: {
    position: 'absolute',
    bottom: 100,
    right: 20,
    width: 60,
    height: 60,
    backgroundColor: '#f4a261',
    borderWidth: 3,
    borderColor: '#8b4513',
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    transform: [{ rotate: '-5deg' }],
    shadowColor: '#8b4513',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 10,
  },
  fabIcon: {
    fontSize: 24,
  },
  bottomNav: {
    backgroundColor: 'rgba(139, 69, 19, 0.3)',
    paddingVertical: 12,
    borderTopWidth: 3,
    borderTopColor: 'rgba(139, 69, 19, 0.5)',
    borderStyle: 'dashed',
  },
  navTabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  navTab: {
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'transparent',
    minWidth: 48,
    minHeight: 48,
    justifyContent: 'center',
  },
  navTabActive: {
    backgroundColor: '#f4a261',
    borderColor: '#8b4513',
    transform: [{ rotate: '1deg' }],
  },
  navIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  navLabel: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#8b4513',
    fontFamily: 'Times New Roman',
  },
});
