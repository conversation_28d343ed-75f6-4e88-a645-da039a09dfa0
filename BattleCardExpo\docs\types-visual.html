<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🃏 PetingGame - Types & Interfaces 視覺化</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
        }

        .architecture-diagram {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin: 40px 0;
            position: relative;
        }

        .layer {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            transition: transform 0.3s ease;
        }

        .layer:hover {
            transform: translateY(-5px);
        }

        .layer-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
            padding: 10px;
            border-radius: 8px;
            color: white;
        }

        .layer.core .layer-title { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .layer.systems .layer-title { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .layer.utils .layer-title { background: linear-gradient(135deg, #2ecc71, #27ae60); }

        .module {
            background: #f8f9fa;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .module:hover {
            background: #e3f2fd;
            transform: translateX(5px);
        }

        .module.config { border-left-color: #f39c12; }
        .module.battle { border-left-color: #e67e22; }
        .module.gacha { border-left-color: #3498db; }
        .module.ui { border-left-color: #9b59b6; }

        .module-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .module-desc {
            font-size: 0.9em;
            color: #7f8c8d;
        }

        .type-list {
            list-style: none;
            margin-top: 10px;
        }

        .type-item {
            background: white;
            margin: 5px 0;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 0.85em;
            border-left: 2px solid #3498db;
        }

        .connections {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .connection-line {
            position: absolute;
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 3px;
            border-radius: 2px;
            opacity: 0.6;
        }

        .flow-diagram {
            margin: 40px 0;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .flow-title {
            text-align: center;
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 30px;
        }

        .flow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .flow-step {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            min-width: 150px;
            position: relative;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .flow-step::after {
            content: '→';
            position: absolute;
            right: -30px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5em;
            color: #667eea;
        }

        .flow-step:last-child::after {
            display: none;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .details-section {
            margin: 40px 0;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .details-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .type-hierarchy {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .hierarchy-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #3498db;
        }

        .hierarchy-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .hierarchy-content {
            color: #7f8c8d;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .architecture-diagram {
                grid-template-columns: 1fr;
            }
            
            .flow-steps {
                flex-direction: column;
            }
            
            .flow-step::after {
                content: '↓';
                right: 50%;
                top: 100%;
                transform: translateX(50%);
            }
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .interactive-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .highlight {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
            100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🃏 PetingGame Types & Interfaces</h1>
            <p>TypeScript 類型系統視覺化架構圖</p>
            <div style="margin-top: 20px;">
                <button class="interactive-btn" onclick="highlightFlow()">🔍 顯示數據流</button>
                <button class="interactive-btn" onclick="showDetails()">📊 顯示詳情</button>
                <button class="interactive-btn" onclick="resetView()">🔄 重置視圖</button>
            </div>
        </div>

        <!-- Architecture Overview -->
        <div class="architecture-diagram" id="architecture">
            <div class="connections">
                <div class="connection-line" style="top: 50%; left: 33%; width: 34%; transform: translateY(-50%);"></div>
                <div class="connection-line" style="top: 50%; left: 67%; width: 33%; transform: translateY(-50%);"></div>
            </div>

            <!-- Core Types Layer -->
            <div class="layer core">
                <div class="layer-title">🎯 核心類型</div>
                <div class="module">
                    <div class="module-name">Card</div>
                    <div class="module-desc">卡牌基礎數據結構</div>
                    <ul class="type-list">
                        <li class="type-item">id, name, race, rarity</li>
                        <li class="type-item">level, stats, tags</li>
                        <li class="type-item">skills, isOwned</li>
                    </ul>
                </div>
                <div class="module">
                    <div class="module-name">BattleCard</div>
                    <div class="module-desc">戰鬥中的卡牌狀態</div>
                    <ul class="type-list">
                        <li class="type-item">currentHealth, actionBar</li>
                        <li class="type-item">buffs, debuffs</li>
                        <li class="type-item">isAlive, position, team</li>
                    </ul>
                </div>
                <div class="module">
                    <div class="module-name">Team & PlayerData</div>
                    <div class="module-desc">隊伍配置和玩家數據</div>
                    <ul class="type-list">
                        <li class="type-item">cards[], totalPower</li>
                        <li class="type-item">gold, diamonds</li>
                        <li class="type-item">settings, statistics</li>
                    </ul>
                </div>
            </div>

            <!-- System Modules Layer -->
            <div class="layer systems">
                <div class="layer-title">⚙️ 系統模塊</div>
                <div class="module config">
                    <div class="module-name">Config System</div>
                    <div class="module-desc">配置管理</div>
                    <ul class="type-list">
                        <li class="type-item">CardConfig, SkillConfig</li>
                        <li class="type-item">StageConfig, GachaConfig</li>
                        <li class="type-item">ConfigManager</li>
                    </ul>
                </div>
                <div class="module battle">
                    <div class="module-name">Battle System</div>
                    <div class="module-desc">戰鬥邏輯</div>
                    <ul class="type-list">
                        <li class="type-item">Battle, BattleAction</li>
                        <li class="type-item">AIDecision, BattleManager</li>
                        <li class="type-item">DamageCalculation</li>
                    </ul>
                </div>
                <div class="module gacha">
                    <div class="module-name">Gacha System</div>
                    <div class="module-desc">抽卡系統</div>
                    <ul class="type-list">
                        <li class="type-item">GachaRequest, GachaResult</li>
                        <li class="type-item">GachaManager, GachaHistory</li>
                        <li class="type-item">GachaAnimation</li>
                    </ul>
                </div>
                <div class="module ui">
                    <div class="module-name">UI System</div>
                    <div class="module-desc">界面組件</div>
                    <ul class="type-list">
                        <li class="type-item">Theme, ComponentProps</li>
                        <li class="type-item">AnimationConfig</li>
                        <li class="type-item">ResponsiveConfig</li>
                    </ul>
                </div>
            </div>

            <!-- Utils Layer -->
            <div class="layer utils">
                <div class="layer-title">🛠️ 工具支持</div>
                <div class="module">
                    <div class="module-name">Type Utils</div>
                    <div class="module-desc">類型工具函數</div>
                    <ul class="type-list">
                        <li class="type-item">isCard(), isBattleCard()</li>
                        <li class="type-item">parseRarity(), parseTags()</li>
                        <li class="type-item">Type Guards</li>
                    </ul>
                </div>
                <div class="module">
                    <div class="module-name">Constants</div>
                    <div class="module-desc">常量定義</div>
                    <ul class="type-list">
                        <li class="type-item">RARITY_CONFIG</li>
                        <li class="type-item">RACE_CONFIG</li>
                        <li class="type-item">GAME_CONFIG</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Data Flow -->
        <div class="flow-diagram">
            <div class="flow-title">🔄 數據流向</div>
            <div class="flow-steps">
                <div class="flow-step">
                    <strong>Core Types</strong><br>
                    基礎數據定義
                </div>
                <div class="flow-step">
                    <strong>Config Loading</strong><br>
                    配置文件載入
                </div>
                <div class="flow-step">
                    <strong>Battle Logic</strong><br>
                    戰鬥邏輯處理
                </div>
                <div class="flow-step">
                    <strong>UI Rendering</strong><br>
                    界面渲染顯示
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">6</div>
                <div class="stat-label">類型模塊</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">50+</div>
                <div class="stat-label">接口定義</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">15+</div>
                <div class="stat-label">枚舉類型</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">30+</div>
                <div class="stat-label">工具函數</div>
            </div>
        </div>

        <!-- Detailed Information -->
        <div class="details-section" id="details" style="display: none;">
            <div class="details-title">📋 詳細類型層次</div>
            <div class="type-hierarchy">
                <div class="hierarchy-item">
                    <div class="hierarchy-title">🎯 核心數據類型</div>
                    <div class="hierarchy-content">
                        <strong>Card:</strong> 卡牌基礎屬性，包含 ID、名稱、種族、稀有度等<br>
                        <strong>BattleCard:</strong> 繼承 Card，添加戰鬥狀態<br>
                        <strong>Team:</strong> 隊伍配置，包含多張卡牌<br>
                        <strong>PlayerData:</strong> 玩家完整數據
                    </div>
                </div>
                <div class="hierarchy-item">
                    <div class="hierarchy-title">🔧 配置系統</div>
                    <div class="hierarchy-content">
                        <strong>CardConfig:</strong> 卡牌配置數據<br>
                        <strong>SkillConfig:</strong> 技能配置數據<br>
                        <strong>StageConfig:</strong> 關卡配置數據<br>
                        <strong>ConfigManager:</strong> 統一配置管理
                    </div>
                </div>
                <div class="hierarchy-item">
                    <div class="hierarchy-title">⚔️ 戰鬥系統</div>
                    <div class="hierarchy-content">
                        <strong>Battle:</strong> 戰鬥實例管理<br>
                        <strong>BattleAction:</strong> 戰鬥行動定義<br>
                        <strong>AIDecision:</strong> AI 決策邏輯<br>
                        <strong>BattleManager:</strong> 戰鬥流程控制
                    </div>
                </div>
                <div class="hierarchy-item">
                    <div class="hierarchy-title">🎴 抽卡系統</div>
                    <div class="hierarchy-content">
                        <strong>GachaRequest:</strong> 抽卡請求<br>
                        <strong>GachaResult:</strong> 抽卡結果<br>
                        <strong>GachaManager:</strong> 抽卡邏輯管理<br>
                        <strong>GachaHistory:</strong> 抽卡歷史記錄
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function highlightFlow() {
            const modules = document.querySelectorAll('.module, .flow-step');
            modules.forEach((module, index) => {
                setTimeout(() => {
                    module.classList.add('highlight');
                    setTimeout(() => {
                        module.classList.remove('highlight');
                    }, 1000);
                }, index * 200);
            });
        }

        function showDetails() {
            const details = document.getElementById('details');
            details.style.display = details.style.display === 'none' ? 'block' : 'none';
        }

        function resetView() {
            document.querySelectorAll('.highlight').forEach(el => {
                el.classList.remove('highlight');
            });
            document.getElementById('details').style.display = 'none';
        }

        // 添加模塊點擊效果
        document.querySelectorAll('.module').forEach(module => {
            module.addEventListener('click', function() {
                this.classList.toggle('highlight');
            });
        });
    </script>
</body>
</html>
