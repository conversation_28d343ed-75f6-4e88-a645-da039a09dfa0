{"version": 3, "names": ["_color", "_interopRequireDefault", "require", "_colors", "e", "__esModule", "default", "MIN_WIDTH", "exports", "MAX_WIDTH", "getDisabledColor", "theme", "isV3", "colors", "onSurfaceDisabled", "color", "dark", "white", "black", "alpha", "rgb", "string", "getTitleColor", "disabled", "onSurface", "text", "getIconColor", "onSurfaceVariant", "getRippleColor", "customRippleColor", "undefined", "getMenuItemColor", "titleColor", "iconColor", "rippleColor", "getContentMaxWidth", "iconWidth", "leadingIcon", "trailingIcon"], "sourceRoot": "../../../../src", "sources": ["components/Menu/utils.ts"], "mappings": ";;;;;;AAEA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAD,OAAA;AAA6D,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAItD,MAAMG,SAAS,GAAAC,OAAA,CAAAD,SAAA,GAAG,GAAG;AACrB,MAAME,SAAS,GAAAD,OAAA,CAAAC,SAAA,GAAG,GAAG;AAe5B,MAAMC,gBAAgB,GAAIC,KAAoB,IAAK;EACjD,IAAIA,KAAK,CAACC,IAAI,EAAE;IACd,OAAOD,KAAK,CAACE,MAAM,CAACC,iBAAiB;EACvC;EAEA,OAAO,IAAAC,cAAK,EAACJ,KAAK,CAACK,IAAI,GAAGC,aAAK,GAAGC,aAAK,CAAC,CACrCC,KAAK,CAAC,IAAI,CAAC,CACXC,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;AACb,CAAC;AAED,MAAMC,aAAa,GAAGA,CAAC;EAAEX,KAAK;EAAEY;AAAqB,CAAC,KAAK;EACzD,IAAIA,QAAQ,EAAE;IACZ,OAAOb,gBAAgB,CAACC,KAAK,CAAC;EAChC;EAEA,IAAIA,KAAK,CAACC,IAAI,EAAE;IACd,OAAOD,KAAK,CAACE,MAAM,CAACW,SAAS;EAC/B;EAEA,OAAO,IAAAT,cAAK,EAACJ,KAAK,CAACE,MAAM,CAACY,IAAI,CAAC,CAACN,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMK,YAAY,GAAGA,CAAC;EAAEf,KAAK;EAAEY;AAAqB,CAAC,KAAK;EACxD,IAAIA,QAAQ,EAAE;IACZ,OAAOb,gBAAgB,CAACC,KAAK,CAAC;EAChC;EAEA,IAAIA,KAAK,CAACC,IAAI,EAAE;IACd,OAAOD,KAAK,CAACE,MAAM,CAACc,gBAAgB;EACtC;EAEA,OAAO,IAAAZ,cAAK,EAACJ,KAAK,CAACE,MAAM,CAACY,IAAI,CAAC,CAACN,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMO,cAAc,GAAGA,CAAC;EACtBjB,KAAK;EACLkB;AAC4B,CAAC,KAAK;EAClC,IAAIA,iBAAiB,EAAE;IACrB,OAAOA,iBAAiB;EAC1B;EAEA,IAAIlB,KAAK,CAACC,IAAI,EAAE;IACd,OAAO,IAAAG,cAAK,EAACJ,KAAK,CAACE,MAAM,CAACc,gBAAgB,CAAC,CAACR,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EACxE;EAEA,OAAOS,SAAS;AAClB,CAAC;AAEM,MAAMC,gBAAgB,GAAGA,CAAC;EAC/BpB,KAAK;EACLY,QAAQ;EACRM;AACU,CAAC,KAAK;EAChB,OAAO;IACLG,UAAU,EAAEV,aAAa,CAAC;MAAEX,KAAK;MAAEY;IAAS,CAAC,CAAC;IAC9CU,SAAS,EAAEP,YAAY,CAAC;MAAEf,KAAK;MAAEY;IAAS,CAAC,CAAC;IAC5CW,WAAW,EAAEN,cAAc,CAAC;MAAEjB,KAAK;MAAEkB;IAAkB,CAAC;EAC1D,CAAC;AACH,CAAC;AAACrB,OAAA,CAAAuB,gBAAA,GAAAA,gBAAA;AAEK,MAAMI,kBAAkB,GAAGA,CAAC;EACjCvB,IAAI;EACJwB,SAAS;EACTC,WAAW;EACXC;AACY,CAAC,KAAK;EAClB,IAAI1B,IAAI,EAAE;IACR,IAAIyB,WAAW,IAAIC,YAAY,EAAE;MAC/B,OAAO7B,SAAS,IAAI,CAAC,GAAG2B,SAAS,GAAG,EAAE,CAAC;IACzC;IAEA,IAAIC,WAAW,IAAIC,YAAY,EAAE;MAC/B,OAAO7B,SAAS,IAAI2B,SAAS,GAAG,EAAE,CAAC;IACrC;IAEA,OAAO3B,SAAS,GAAG,EAAE;EACvB;EAEA,IAAI4B,WAAW,EAAE;IACf,OAAO5B,SAAS,IAAI2B,SAAS,GAAG,EAAE,CAAC;EACrC;EAEA,OAAO3B,SAAS,GAAG,EAAE;AACvB,CAAC;AAACD,OAAA,CAAA2B,kBAAA,GAAAA,kBAAA", "ignoreList": []}