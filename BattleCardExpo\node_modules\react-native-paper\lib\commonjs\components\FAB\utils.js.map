{"version": 3, "names": ["_reactNative", "require", "_color", "_interopRequireDefault", "_colors", "_getContrastingColor", "e", "__esModule", "default", "getCombinedStyles", "isAnimatedFromRight", "isIconStatic", "distance", "animFAB", "isRTL", "I18nManager", "defaultPositionStyles", "left", "right", "undefined", "combinedStyles", "innerWrapper", "iconWrapper", "absoluteFill", "animatedFromRight", "animatedFromRightRTL", "animatedFromLeft", "animatedFromLeftRTL", "transform", "translateX", "interpolate", "inputRange", "outputRange", "Math", "abs", "exports", "getBackgroundColor", "theme", "isVariant", "disabled", "customBackgroundColor", "_theme$colors", "isV3", "colors", "surfaceDisabled", "primaryContainer", "secondaryContainer", "tertiaryContainer", "elevation", "level3", "dark", "color", "white", "alpha", "rgb", "string", "black", "accent", "getForegroundColor", "backgroundColor", "customColor", "onSurfaceDisabled", "onPrimaryContainer", "onSecondaryContainer", "onTertiaryContainer", "primary", "getContrastingColor", "getFABColors", "variant", "customRippleColor", "variantToCompare", "baseFABColorProps", "foregroundColor", "rippleColor", "getLabelColor", "onSurface", "text", "fade", "getBackdropColor", "customBackdropColor", "_theme$colors2", "background", "backdrop", "getStackedFABBackgroundColor", "surface", "getFABGroupColors", "labelColor", "backdropColor", "stackedFABBackgroundColor", "standardSize", "height", "width", "borderRadius", "smallSize", "v3SmallSize", "v3MediumSize", "v3LargeSize", "getCustomFabSize", "customSize", "roundness", "getFabStyle", "size", "extended", "paddingHorizontal", "v3Extended", "getExtendedFabDimensions", "getExtendedFabStyle", "cachedContext", "getCanvasContext", "canvas", "document", "createElement", "getContext", "getLabelSizeWeb", "ref", "Platform", "OS", "current", "canvasContext", "elementStyles", "window", "getComputedStyle", "font", "metrics", "measureText", "innerText", "fontBoundingBoxAscent", "fontBoundingBoxDescent"], "sourceRoot": "../../../../src", "sources": ["components/FAB/utils.ts"], "mappings": ";;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AAQA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,OAAA,GAAAH,OAAA;AAEA,IAAAI,oBAAA,GAAAF,sBAAA,CAAAF,OAAA;AAAkE,SAAAE,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAuB3D,MAAMG,iBAAiB,GAAGA,CAAC;EAChCC,mBAAmB;EACnBC,YAAY;EACZC,QAAQ;EACRC;AACsB,CAAC,KAAqB;EAC5C,MAAM;IAAEC;EAAM,CAAC,GAAGC,wBAAW;EAE7B,MAAMC,qBAAqB,GAAG;IAAEC,IAAI,EAAE,CAACL,QAAQ;IAAEM,KAAK,EAAEC;EAAU,CAAC;EAEnE,MAAMC,cAA8B,GAAG;IACrCC,YAAY,EAAE;MACZ,GAAGL;IACL,CAAC;IACDM,WAAW,EAAE;MACX,GAAGN;IACL,CAAC;IACDO,YAAY,EAAE,CAAC;EACjB,CAAC;EAED,MAAMC,iBAAiB,GAAGd,mBAAmB,IAAI,CAACI,KAAK;EACvD,MAAMW,oBAAoB,GAAGf,mBAAmB,IAAII,KAAK;EACzD,MAAMY,gBAAgB,GAAG,CAAChB,mBAAmB,IAAI,CAACI,KAAK;EACvD,MAAMa,mBAAmB,GAAG,CAACjB,mBAAmB,IAAII,KAAK;EAEzD,IAAIU,iBAAiB,EAAE;IACrBJ,cAAc,CAACC,YAAY,CAACO,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEhB,OAAO,CAACiB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAACnB,QAAQ,EAAE,CAAC,CAAC;QACzBoB,WAAW,EAAE,CAACpB,QAAQ,EAAE,CAAC;MAC3B,CAAC;IACH,CAAC,CACF;IACDQ,cAAc,CAACE,WAAW,CAACM,SAAS,GAAG,CACrC;MACEC,UAAU,EAAElB,YAAY,GAAG,CAAC,GAAGE;IACjC,CAAC,CACF;IACDO,cAAc,CAACG,YAAY,CAACK,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEhB,OAAO,CAACiB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAACnB,QAAQ,EAAE,CAAC,CAAC;QACzBoB,WAAW,EAAE,CAACC,IAAI,CAACC,GAAG,CAACtB,QAAQ,CAAC,GAAG,CAAC,EAAEqB,IAAI,CAACC,GAAG,CAACtB,QAAQ,CAAC;MAC1D,CAAC;IACH,CAAC,CACF;EACH,CAAC,MAAM,IAAIa,oBAAoB,EAAE;IAC/BL,cAAc,CAACE,WAAW,CAACM,SAAS,GAAG,CACrC;MACEC,UAAU,EAAElB,YAAY,GACpB,CAAC,GACDE,OAAO,CAACiB,WAAW,CAAC;QAClBC,UAAU,EAAE,CAACnB,QAAQ,EAAE,CAAC,CAAC;QACzBoB,WAAW,EAAE,CAAC,CAACpB,QAAQ,EAAE,CAAC;MAC5B,CAAC;IACP,CAAC,CACF;IACDQ,cAAc,CAACC,YAAY,CAACO,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEhB,OAAO,CAACiB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAACnB,QAAQ,EAAE,CAAC,CAAC;QACzBoB,WAAW,EAAE,CAAC,CAACpB,QAAQ,EAAE,CAAC;MAC5B,CAAC;IACH,CAAC,CACF;IACDQ,cAAc,CAACG,YAAY,CAACK,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEhB,OAAO,CAACiB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAACnB,QAAQ,EAAE,CAAC,CAAC;QACzBoB,WAAW,EAAE,CAAC,CAAC,EAAEpB,QAAQ;MAC3B,CAAC;IACH,CAAC,CACF;EACH,CAAC,MAAM,IAAIc,gBAAgB,EAAE;IAC3BN,cAAc,CAACE,WAAW,CAACM,SAAS,GAAG,CACrC;MACEC,UAAU,EAAElB,YAAY,GACpBC,QAAQ,GACRC,OAAO,CAACiB,WAAW,CAAC;QAClBC,UAAU,EAAE,CAAC,CAAC,EAAEnB,QAAQ,CAAC;QACzBoB,WAAW,EAAE,CAACpB,QAAQ,EAAEA,QAAQ,GAAG,CAAC;MACtC,CAAC;IACP,CAAC,CACF;IACDQ,cAAc,CAACC,YAAY,CAACO,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEhB;IACd,CAAC,CACF;IACDO,cAAc,CAACG,YAAY,CAACK,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEhB,OAAO,CAACiB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAAC,CAAC,EAAEnB,QAAQ,CAAC;QACzBoB,WAAW,EAAE,CAAC,CAAC,EAAEC,IAAI,CAACC,GAAG,CAACtB,QAAQ,CAAC,GAAG,CAAC;MACzC,CAAC;IACH,CAAC,CACF;EACH,CAAC,MAAM,IAAIe,mBAAmB,EAAE;IAC9BP,cAAc,CAACE,WAAW,CAACM,SAAS,GAAG,CACrC;MACEC,UAAU,EAAElB,YAAY,GACpBE,OAAO,CAACiB,WAAW,CAAC;QAClBC,UAAU,EAAE,CAAC,CAAC,EAAEnB,QAAQ,CAAC;QACzBoB,WAAW,EAAE,CAAC,CAACpB,QAAQ,EAAE,CAACA,QAAQ,GAAG,CAAC;MACxC,CAAC,CAAC,GACF,CAACA;IACP,CAAC,CACF;IACDQ,cAAc,CAACC,YAAY,CAACO,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEhB,OAAO,CAACiB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAAC,CAAC,EAAEnB,QAAQ,CAAC;QACzBoB,WAAW,EAAE,CAAC,CAAC,EAAE,CAACpB,QAAQ;MAC5B,CAAC;IACH,CAAC,CACF;IACDQ,cAAc,CAACG,YAAY,CAACK,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEhB,OAAO,CAACiB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAAC,CAAC,EAAEnB,QAAQ,CAAC;QACzBoB,WAAW,EAAE,CAAC,CAAC,EAAE,CAACpB,QAAQ;MAC5B,CAAC;IACH,CAAC,CACF;EACH;EAEA,OAAOQ,cAAc;AACvB,CAAC;AAACe,OAAA,CAAA1B,iBAAA,GAAAA,iBAAA;AAEF,MAAM2B,kBAAkB,GAAGA,CAAC;EAC1BC,KAAK;EACLC,SAAS;EACTC,QAAQ;EACRC;AACkD,CAAC,KAAK;EAAA,IAAAC,aAAA;EACxD,IAAID,qBAAqB,IAAI,CAACD,QAAQ,EAAE;IACtC,OAAOC,qBAAqB;EAC9B;EAEA,IAAIH,KAAK,CAACK,IAAI,EAAE;IACd,IAAIH,QAAQ,EAAE;MACZ,OAAOF,KAAK,CAACM,MAAM,CAACC,eAAe;IACrC;IAEA,IAAIN,SAAS,CAAC,SAAS,CAAC,EAAE;MACxB,OAAOD,KAAK,CAACM,MAAM,CAACE,gBAAgB;IACtC;IAEA,IAAIP,SAAS,CAAC,WAAW,CAAC,EAAE;MAC1B,OAAOD,KAAK,CAACM,MAAM,CAACG,kBAAkB;IACxC;IAEA,IAAIR,SAAS,CAAC,UAAU,CAAC,EAAE;MACzB,OAAOD,KAAK,CAACM,MAAM,CAACI,iBAAiB;IACvC;IAEA,IAAIT,SAAS,CAAC,SAAS,CAAC,EAAE;MACxB,OAAOD,KAAK,CAACM,MAAM,CAACK,SAAS,CAACC,MAAM;IACtC;EACF;EAEA,IAAIV,QAAQ,EAAE;IACZ,IAAIF,KAAK,CAACa,IAAI,EAAE;MACd,OAAO,IAAAC,cAAK,EAACC,aAAK,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IAChD;IACA,OAAO,IAAAJ,cAAK,EAACK,aAAK,CAAC,CAACH,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAChD;;EAEA;EACA,QAAAd,aAAA,GAAOJ,KAAK,CAACM,MAAM,cAAAF,aAAA,uBAAZA,aAAA,CAAcgB,MAAM;AAC7B,CAAC;AAED,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BrB,KAAK;EACLC,SAAS;EACTC,QAAQ;EACRoB,eAAe;EACfC;AAC6D,CAAC,KAAK;EACnE,IAAI,OAAOA,WAAW,KAAK,WAAW,IAAI,CAACrB,QAAQ,EAAE;IACnD,OAAOqB,WAAW;EACpB;EAEA,IAAIvB,KAAK,CAACK,IAAI,EAAE;IACd,IAAIH,QAAQ,EAAE;MACZ,OAAOF,KAAK,CAACM,MAAM,CAACkB,iBAAiB;IACvC;IAEA,IAAIvB,SAAS,CAAC,SAAS,CAAC,EAAE;MACxB,OAAOD,KAAK,CAACM,MAAM,CAACmB,kBAAkB;IACxC;IAEA,IAAIxB,SAAS,CAAC,WAAW,CAAC,EAAE;MAC1B,OAAOD,KAAK,CAACM,MAAM,CAACoB,oBAAoB;IAC1C;IAEA,IAAIzB,SAAS,CAAC,UAAU,CAAC,EAAE;MACzB,OAAOD,KAAK,CAACM,MAAM,CAACqB,mBAAmB;IACzC;IAEA,IAAI1B,SAAS,CAAC,SAAS,CAAC,EAAE;MACxB,OAAOD,KAAK,CAACM,MAAM,CAACsB,OAAO;IAC7B;EACF;EAEA,IAAI1B,QAAQ,EAAE;IACZ,IAAIF,KAAK,CAACa,IAAI,EAAE;MACd,OAAO,IAAAC,cAAK,EAACC,aAAK,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IAChD;IACA,OAAO,IAAAJ,cAAK,EAACK,aAAK,CAAC,CAACH,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAChD;EAEA,IAAII,eAAe,EAAE;IACnB,OAAO,IAAAO,4BAAmB,EACxBP,eAAe,IAAIP,aAAK,EACxBA,aAAK,EACL,oBACF,CAAC;EACH;EAEA,OAAO,IAAAc,4BAAmB,EAACd,aAAK,EAAEA,aAAK,EAAE,oBAAoB,CAAC;AAChE,CAAC;AAEM,MAAMe,YAAY,GAAGA,CAAC;EAC3B9B,KAAK;EACL+B,OAAO;EACP7B,QAAQ;EACRqB,WAAW;EACXpB,qBAAqB;EACrB6B;AAQF,CAAC,KAAK;EACJ,MAAM/B,SAAS,GAAIgC,gBAAyB,IAAK;IAC/C,OAAOF,OAAO,KAAKE,gBAAgB;EACrC,CAAC;EAED,MAAMC,iBAAiB,GAAG;IAAElC,KAAK;IAAEC,SAAS;IAAEC;EAAS,CAAC;EAExD,MAAMoB,eAAe,GAAGvB,kBAAkB,CAAC;IACzC,GAAGmC,iBAAiB;IACpB/B;EACF,CAAC,CAAC;EAEF,MAAMgC,eAAe,GAAGd,kBAAkB,CAAC;IACzC,GAAGa,iBAAiB;IACpBX,WAAW;IACXD;EACF,CAAC,CAAC;EAEF,OAAO;IACLA,eAAe;IACfa,eAAe;IACfC,WAAW,EACTJ,iBAAiB,IAAI,IAAAlB,cAAK,EAACqB,eAAe,CAAC,CAACnB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC;EACzE,CAAC;AACH,CAAC;AAACpB,OAAA,CAAAgC,YAAA,GAAAA,YAAA;AAEF,MAAMO,aAAa,GAAGA,CAAC;EAAErC;AAAgC,CAAC,KAAK;EAC7D,IAAIA,KAAK,CAACK,IAAI,EAAE;IACd,OAAOL,KAAK,CAACM,MAAM,CAACgC,SAAS;EAC/B;EAEA,IAAItC,KAAK,CAACa,IAAI,EAAE;IACd,OAAOb,KAAK,CAACM,MAAM,CAACiC,IAAI;EAC1B;EAEA,OAAO,IAAAzB,cAAK,EAACd,KAAK,CAACM,MAAM,CAACiC,IAAI,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAACvB,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AAC3D,CAAC;AAED,MAAMuB,gBAAgB,GAAGA,CAAC;EACxBzC,KAAK;EACL0C;AAIF,CAAC,KAAK;EAAA,IAAAC,cAAA;EACJ,IAAID,mBAAmB,EAAE;IACvB,OAAOA,mBAAmB;EAC5B;EACA,IAAI1C,KAAK,CAACK,IAAI,EAAE;IACd,OAAO,IAAAS,cAAK,EAACd,KAAK,CAACM,MAAM,CAACsC,UAAU,CAAC,CAAC5B,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAClE;EACA,QAAAyB,cAAA,GAAO3C,KAAK,CAACM,MAAM,cAAAqC,cAAA,uBAAZA,cAAA,CAAcE,QAAQ;AAC/B,CAAC;AAED,MAAMC,4BAA4B,GAAGA,CAAC;EAAE9C;AAAgC,CAAC,KAAK;EAC5E,IAAIA,KAAK,CAACK,IAAI,EAAE;IACd,OAAOL,KAAK,CAACM,MAAM,CAACK,SAAS,CAACC,MAAM;EACtC;EACA,OAAOZ,KAAK,CAACM,MAAM,CAACyC,OAAO;AAC7B,CAAC;AAEM,MAAMC,iBAAiB,GAAGA,CAAC;EAChChD,KAAK;EACL0C;AAIF,CAAC,KAAK;EACJ,OAAO;IACLO,UAAU,EAAEZ,aAAa,CAAC;MAAErC;IAAM,CAAC,CAAC;IACpCkD,aAAa,EAAET,gBAAgB,CAAC;MAAEzC,KAAK;MAAE0C;IAAoB,CAAC,CAAC;IAC/DS,yBAAyB,EAAEL,4BAA4B,CAAC;MAAE9C;IAAM,CAAC;EACnE,CAAC;AACH,CAAC;AAACF,OAAA,CAAAkD,iBAAA,GAAAA,iBAAA;AAEF,MAAMI,YAAY,GAAG;EACnBC,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE,EAAE;EACTC,YAAY,EAAE;AAChB,CAAC;AACD,MAAMC,SAAS,GAAG;EAChBH,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE,EAAE;EACTC,YAAY,EAAE;AAChB,CAAC;AACD,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE;AACT,CAAC;AACD,MAAMI,YAAY,GAAG;EACnBL,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE;AACT,CAAC;AACD,MAAMK,WAAW,GAAG;EAClBN,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE;AACT,CAAC;AAED,MAAMM,gBAAgB,GAAGA,CAACC,UAAkB,EAAEC,SAAiB,MAAM;EACnET,MAAM,EAAEQ,UAAU;EAClBP,KAAK,EAAEO,UAAU;EACjBN,YAAY,EAAEO,SAAS,KAAK,CAAC,GAAG,CAAC,GAAGD,UAAU,GAAGC;AACnD,CAAC,CAAC;AAEK,MAAMC,WAAW,GAAGA,CAAC;EAC1BC,IAAI;EACJhE,KAAK;EACL6D;AAKF,CAAC,KAAK;EACJ,MAAM;IAAExD,IAAI;IAAEyD;EAAU,CAAC,GAAG9D,KAAK;EAEjC,IAAI6D,UAAU,EAAE,OAAOD,gBAAgB,CAACC,UAAU,EAAEC,SAAS,CAAC;EAE9D,IAAIzD,IAAI,EAAE;IACR,QAAQ2D,IAAI;MACV,KAAK,OAAO;QACV,OAAO;UAAE,GAAGP,WAAW;UAAEF,YAAY,EAAE,CAAC,GAAGO;QAAU,CAAC;MACxD,KAAK,QAAQ;QACX,OAAO;UAAE,GAAGJ,YAAY;UAAEH,YAAY,EAAE,CAAC,GAAGO;QAAU,CAAC;MACzD,KAAK,OAAO;QACV,OAAO;UAAE,GAAGH,WAAW;UAAEJ,YAAY,EAAE,CAAC,GAAGO;QAAU,CAAC;IAC1D;EACF;EAEA,IAAIE,IAAI,KAAK,OAAO,EAAE;IACpB,OAAOR,SAAS;EAClB;EACA,OAAOJ,YAAY;AACrB,CAAC;AAACtD,OAAA,CAAAiE,WAAA,GAAAA,WAAA;AAEF,MAAME,QAAQ,GAAG;EACfZ,MAAM,EAAE,EAAE;EACVa,iBAAiB,EAAE;AACrB,CAAC;AAED,MAAMC,UAAU,GAAG;EACjBd,MAAM,EAAE,EAAE;EACVE,YAAY,EAAE,EAAE;EAChBW,iBAAiB,EAAE;AACrB,CAAC;AAED,MAAME,wBAAwB,GAAIP,UAAkB,KAAM;EACxDR,MAAM,EAAEQ,UAAU;EAClBK,iBAAiB,EAAE;AACrB,CAAC,CAAC;AAEK,MAAMG,mBAAmB,GAAGA,CAAC;EAClCR,UAAU;EACV7D;AAIF,CAAC,KAAK;EACJ,IAAI6D,UAAU,EAAE,OAAOO,wBAAwB,CAACP,UAAU,CAAC;EAE3D,MAAM;IAAExD;EAAK,CAAC,GAAGL,KAAK;EAEtB,OAAOK,IAAI,GAAG8D,UAAU,GAAGF,QAAQ;AACrC,CAAC;AAACnE,OAAA,CAAAuE,mBAAA,GAAAA,mBAAA;AAEF,IAAIC,aAA8C,GAAG,IAAI;AAEzD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAC7B,IAAID,aAAa,EAAE;IACjB,OAAOA,aAAa;EACtB;EAEA,MAAME,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/CJ,aAAa,GAAGE,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;EAEvC,OAAOL,aAAa;AACtB,CAAC;AAEM,MAAMM,eAAe,GAAIC,GAAyC,IAAK;EAC5E,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAIF,GAAG,CAACG,OAAO,KAAK,IAAI,EAAE;IACjD,OAAO,IAAI;EACb;EAEA,MAAMC,aAAa,GAAGV,gBAAgB,CAAC,CAAC;EAExC,IAAI,CAACU,aAAa,EAAE;IAClB,OAAO,IAAI;EACb;EAEA,MAAMC,aAAa,GAAGC,MAAM,CAACC,gBAAgB,CAACP,GAAG,CAACG,OAAO,CAAC;EAC1DC,aAAa,CAACI,IAAI,GAAGH,aAAa,CAACG,IAAI;EAEvC,MAAMC,OAAO,GAAGL,aAAa,CAACM,WAAW,CAACV,GAAG,CAACG,OAAO,CAACQ,SAAS,CAAC;EAEhE,OAAO;IACLlC,KAAK,EAAEgC,OAAO,CAAChC,KAAK;IACpBD,MAAM,EACJ,CAACiC,OAAO,CAACG,qBAAqB,IAAI,CAAC,KAClCH,OAAO,CAACI,sBAAsB,IAAI,CAAC;EACxC,CAAC;AACH,CAAC;AAAC5F,OAAA,CAAA8E,eAAA,GAAAA,eAAA", "ignoreList": []}