<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡牌組件 - 3種設計風格</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            overflow-x: hidden;
            padding: 20px;
        }

        .design-section {
            max-width: 375px;
            margin: 0 auto 60px auto;
            position: relative;
            border: 2px solid #333;
            border-radius: 10px;
            padding: 20px;
        }

        .design-title {
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            background: #000;
            padding: 5px 15px;
            border-radius: 5px;
            font-size: 16px;
            z-index: 1000;
        }

        .card-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }

        /* ========== 全息投影風格 ========== */
        .holographic {
            background: linear-gradient(135deg, #001122 0%, #003366 50%, #004488 100%);
        }

        .holographic .card {
            width: 100px;
            height: 140px;
            background: linear-gradient(145deg, rgba(0, 255, 255, 0.2), rgba(157, 78, 221, 0.1));
            border: 2px solid #00ffff;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 25px rgba(0, 255, 255, 0.3);
        }

        .holographic .card:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.4), transparent);
            animation: holoScan 3s infinite;
        }

        .holographic .card:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                repeating-linear-gradient(
                    0deg,
                    transparent,
                    transparent 2px,
                    rgba(0, 255, 255, 0.1) 2px,
                    rgba(0, 255, 255, 0.1) 4px
                );
            animation: holoLines 2s infinite;
            pointer-events: none;
        }

        .holographic .card:hover {
            transform: translateY(-8px) rotateY(15deg);
            box-shadow: 0 15px 40px rgba(157, 78, 221, 0.5);
            border-color: #9d4edd;
        }

        .holographic .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px;
            font-size: 10px;
            font-weight: bold;
            color: #00ffff;
            font-family: 'Courier New', monospace;
        }

        .holographic .card-level {
            background: linear-gradient(45deg, #00ffff, #9d4edd);
            padding: 2px 6px;
            border-radius: 0;
            font-size: 8px;
            color: black;
            box-shadow: 0 0 10px #00ffff;
        }

        .holographic .card-icon {
            font-size: 28px;
            text-align: center;
            margin: 10px 0;
            filter: drop-shadow(0 0 10px #00ffff);
            animation: holoFloat 4s infinite ease-in-out;
        }

        .holographic .card-name {
            text-align: center;
            padding: 4px;
            font-size: 11px;
            font-weight: bold;
            color: #00ffff;
            font-family: 'Courier New', monospace;
            text-transform: uppercase;
        }

        .holographic .card-stats {
            display: flex;
            justify-content: space-between;
            padding: 6px;
            font-size: 10px;
            font-weight: bold;
            color: #9d4edd;
            font-family: 'Courier New', monospace;
        }

        .holographic .action-bar {
            margin: 4px 6px;
            height: 6px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 0;
            overflow: hidden;
            border: 1px solid #00ffff;
        }

        .holographic .action-progress {
            height: 100%;
            background: linear-gradient(90deg, #00ffff, #9d4edd);
            border-radius: 0;
            transition: width 0.3s ease;
            box-shadow: 0 0 10px #00ffff;
        }

        .holographic .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 6px;
            margin-top: auto;
        }

        .holographic .race-tag {
            font-size: 8px;
            padding: 2px 4px;
            border: 1px solid #00ffff;
            background: rgba(0, 255, 255, 0.2);
            color: #00ffff;
            font-family: 'Courier New', monospace;
        }

        .holographic .rarity-stars {
            font-size: 10px;
            color: #9d4edd;
            filter: drop-shadow(0 0 5px #9d4edd);
        }

        /* ========== 寶石水晶風格 ========== */
        .crystal {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
        }

        .crystal .card {
            width: 100px;
            height: 140px;
            background: linear-gradient(145deg, #1e40af 0%, #059669 50%, #dc2626 100%);
            border: 3px solid #fbbf24;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 
                0 8px 25px rgba(251, 191, 36, 0.3),
                inset 0 2px 4px rgba(255, 255, 255, 0.2);
        }

        .crystal .card:before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(
                from 0deg at 50% 50%,
                transparent,
                rgba(251, 191, 36, 0.4),
                transparent,
                rgba(30, 64, 175, 0.4),
                transparent,
                rgba(5, 150, 105, 0.4),
                transparent,
                rgba(220, 38, 38, 0.4),
                transparent
            );
            animation: crystalRotate 6s linear infinite;
            z-index: -1;
        }

        .crystal .card:after {
            content: '';
            position: absolute;
            top: 5px;
            left: 5px;
            right: 5px;
            bottom: 5px;
            background: linear-gradient(145deg, rgba(30, 64, 175, 0.8), rgba(5, 150, 105, 0.6));
            border-radius: 8px;
            z-index: 1;
        }

        .crystal .card:hover {
            transform: translateY(-6px) scale(1.05);
            box-shadow: 
                0 15px 40px rgba(251, 191, 36, 0.5),
                inset 0 2px 8px rgba(255, 255, 255, 0.3);
        }

        .crystal .card-content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .crystal .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px;
            font-size: 10px;
            font-weight: bold;
            color: #fbbf24;
        }

        .crystal .card-level {
            background: radial-gradient(circle, #fbbf24, #f59e0b);
            padding: 2px 6px;
            border-radius: 50%;
            font-size: 8px;
            color: white;
            box-shadow: 0 0 15px #fbbf24;
            border: 1px solid #fbbf24;
        }

        .crystal .card-icon {
            font-size: 28px;
            text-align: center;
            margin: 10px 0;
            filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.5));
            animation: crystalPulse 3s infinite ease-in-out;
        }

        .crystal .card-name {
            text-align: center;
            padding: 4px;
            font-size: 11px;
            font-weight: bold;
            color: #fbbf24;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }

        .crystal .card-stats {
            display: flex;
            justify-content: space-between;
            padding: 6px;
            font-size: 10px;
            font-weight: bold;
            color: #f8fafc;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }

        .crystal .action-bar {
            margin: 4px 6px;
            height: 6px;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 3px;
            overflow: hidden;
            border: 1px solid #fbbf24;
        }

        .crystal .action-progress {
            height: 100%;
            background: linear-gradient(90deg, #059669, #fbbf24, #dc2626);
            border-radius: 3px;
            transition: width 0.3s ease;
            box-shadow: 0 0 8px rgba(251, 191, 36, 0.6);
        }

        .crystal .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 6px;
            margin-top: auto;
        }

        .crystal .race-tag {
            font-size: 8px;
            padding: 2px 4px;
            border-radius: 8px;
            background: radial-gradient(circle, rgba(251, 191, 36, 0.3), rgba(251, 191, 36, 0.1));
            color: #fbbf24;
            border: 1px solid #fbbf24;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }

        .crystal .rarity-stars {
            font-size: 10px;
            color: #fbbf24;
            filter: drop-shadow(0 0 5px #fbbf24);
            animation: crystalTwinkle 2s infinite;
        }

        /* ========== 手繪藝術風格 ========== */
        .handdrawn {
            background: linear-gradient(135deg, #f7f3e9 0%, #e8dcc0 50%, #d4c5a0 100%);
            color: #3c2415;
        }

        .handdrawn .card {
            width: 100px;
            height: 140px;
            background: linear-gradient(145deg, #f9f7f4, #f0ede6);
            border: 3px solid #8b4513;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(139, 69, 19, 0.3);
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
        }

        .handdrawn .card:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 30%, rgba(139, 69, 19, 0.1) 2px, transparent 2px),
                radial-gradient(circle at 70% 60%, rgba(139, 69, 19, 0.05) 1px, transparent 1px),
                radial-gradient(circle at 40% 80%, rgba(139, 69, 19, 0.08) 1px, transparent 1px);
            background-size: 20px 20px, 15px 15px, 25px 25px;
        }

        .handdrawn .card:after {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            border: 2px solid #8b4513;
            border-radius: 4px;
            border-style: dashed;
            opacity: 0.3;
        }

        .handdrawn .card:hover {
            transform: translateY(-4px) rotate(2deg);
            box-shadow: 0 12px 30px rgba(139, 69, 19, 0.4);
            filter: drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.2));
        }

        .handdrawn .card-content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .handdrawn .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px;
            font-size: 10px;
            font-weight: bold;
            color: #8b4513;
            font-family: 'Times New Roman', serif;
        }

        .handdrawn .card-level {
            background: #f4a261;
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 8px;
            color: white;
            border: 2px solid #8b4513;
            box-shadow: 2px 2px 0px rgba(139, 69, 19, 0.3);
            transform: rotate(-5deg);
        }

        .handdrawn .card-icon {
            font-size: 28px;
            text-align: center;
            margin: 8px 0;
            filter: sepia(0.3) contrast(1.2);
            animation: handDrawnBob 4s infinite ease-in-out;
            transform: rotate(-2deg);
        }

        .handdrawn .card-name {
            text-align: center;
            padding: 4px;
            font-size: 11px;
            font-weight: bold;
            color: #8b4513;
            font-family: 'Times New Roman', serif;
            transform: rotate(1deg);
            text-decoration: underline;
            text-decoration-color: rgba(139, 69, 19, 0.3);
        }

        .handdrawn .card-stats {
            display: flex;
            justify-content: space-between;
            padding: 6px;
            font-size: 10px;
            font-weight: bold;
            color: #8b4513;
            font-family: 'Times New Roman', serif;
        }

        .handdrawn .stat-attack {
            color: #d2691e;
            transform: rotate(-1deg);
        }

        .handdrawn .stat-speed {
            color: #228b22;
            transform: rotate(1deg);
        }

        .handdrawn .action-bar {
            margin: 4px 6px;
            height: 6px;
            background: rgba(139, 69, 19, 0.2);
            border-radius: 3px;
            overflow: hidden;
            border: 1px solid #8b4513;
            border-style: dashed;
        }

        .handdrawn .action-progress {
            height: 100%;
            background: linear-gradient(90deg, #228b22, #f4a261);
            border-radius: 2px;
            transition: width 0.3s ease;
            position: relative;
        }

        .handdrawn .action-progress:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 2px,
                rgba(255, 255, 255, 0.3) 2px,
                rgba(255, 255, 255, 0.3) 4px
            );
        }

        .handdrawn .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 6px;
            margin-top: auto;
        }

        .handdrawn .race-tag {
            font-size: 8px;
            padding: 2px 6px;
            border-radius: 12px;
            background: #f4a261;
            color: #8b4513;
            border: 2px solid #8b4513;
            font-family: 'Times New Roman', serif;
            transform: rotate(-3deg);
            box-shadow: 1px 1px 0px rgba(139, 69, 19, 0.3);
        }

        .handdrawn .rarity-stars {
            font-size: 10px;
            color: #f4a261;
            transform: rotate(2deg);
            filter: drop-shadow(1px 1px 1px rgba(139, 69, 19, 0.3));
        }

        /* 動畫效果 */
        @keyframes holoScan {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        @keyframes holoLines {
            0% { transform: translateY(0); }
            100% { transform: translateY(4px); }
        }

        @keyframes holoFloat {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-4px) rotate(2deg); }
        }

        @keyframes crystalRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes crystalPulse {
            0%, 100% { transform: scale(1); filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.5)); }
            50% { transform: scale(1.1); filter: drop-shadow(0 4px 12px rgba(251, 191, 36, 0.6)); }
        }

        @keyframes crystalTwinkle {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.2); }
        }

        @keyframes handDrawnBob {
            0%, 100% { transform: translateY(0) rotate(-2deg); }
            50% { transform: translateY(-2px) rotate(0deg); }
        }

        /* 響應式設計 */
        @media (max-width: 360px) {
            .card {
                width: 85px;
                height: 120px;
            }
            
            .card-icon {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <!-- 全息投影風格 -->
    <div class="design-section holographic">
        <div class="design-title">🌐 全息投影風格</div>
        <div class="card-container">
            <!-- 人族卡牌 -->
            <div class="card">
                <div class="card-content" style="display: flex; flex-direction: column; height: 100%; position: relative; z-index: 2;">
                    <div class="card-header">
                        <span class="card-level">L5</span>
                        <span style="color: #9d4edd;">❤️ 85</span>
                    </div>
                    <div class="card-icon">🛡️</div>
                    <div class="card-name">PALADIN.EXE</div>
                    <div class="card-stats">
                        <span class="stat-attack">ATK: 75</span>
                        <span class="stat-speed">SPD: 12</span>
                    </div>
                    <div class="action-bar">
                        <div class="action-progress" style="width: 60%;"></div>
                    </div>
                    <div class="card-footer">
                        <span class="race-tag">HUMAN</span>
                        <span class="rarity-stars">⭐⭐⭐</span>
                    </div>
                </div>
            </div>

            <!-- 精靈卡牌 -->
            <div class="card">
                <div class="card-content" style="display: flex; flex-direction: column; height: 100%; position: relative; z-index: 2;">
                    <div class="card-header">
                        <span class="card-level">L7</span>
                        <span style="color: #9d4edd;">❤️ 65</span>
                    </div>
                    <div class="card-icon">🏹</div>
                    <div class="card-name">ARCHER.EXE</div>
                    <div class="card-stats">
                        <span class="stat-attack">ATK: 90</span>
                        <span class="stat-speed">SPD: 18</span>
                    </div>
                    <div class="action-bar">
                        <div class="action-progress" style="width: 100%;"></div>
                    </div>
                    <div class="card-footer">
                        <span class="race-tag">ELF</span>
                        <span class="rarity-stars">⭐⭐⭐⭐</span>
                    </div>
                </div>
            </div>

            <!-- 龍族卡牌 -->
            <div class="card">
                <div class="card-content" style="display: flex; flex-direction: column; height: 100%; position: relative; z-index: 2;">
                    <div class="card-header">
                        <span class="card-level">L8</span>
                        <span style="color: #9d4edd;">❤️ 150</span>
                    </div>
                    <div class="card-icon">🔥</div>
                    <div class="card-name">DRAGON.EXE</div>
                    <div class="card-stats">
                        <span class="stat-attack">ATK: 130</span>
                        <span class="stat-speed">SPD: 15</span>
                    </div>
                    <div class="action-bar">
                        <div class="action-progress" style="width: 80%;"></div>
                    </div>
                    <div class="card-footer">
                        <span class="race-tag">DRAGON</span>
                        <span class="rarity-stars">⭐⭐⭐⭐⭐</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 寶石水晶風格 -->
    <div class="design-section crystal">
        <div class="design-title">💎 寶石水晶風格</div>
        <div class="card-container">
            <!-- 人族卡牌 -->
            <div class="card">
                <div class="card-content">
                    <div class="card-header">
                        <span class="card-level">5</span>
                        <span>❤️ 85</span>
                    </div>
                    <div class="card-icon">🛡️</div>
                    <div class="card-name">聖騎士</div>
                    <div class="card-stats">
                        <span class="stat-attack">⚔️ 75</span>
                        <span class="stat-speed">⚡ 12</span>
                    </div>
                    <div class="action-bar">
                        <div class="action-progress" style="width: 60%;"></div>
                    </div>
                    <div class="card-footer">
                        <span class="race-tag">人族</span>
                        <span class="rarity-stars">⭐⭐⭐</span>
                    </div>
                </div>
            </div>

            <!-- 精靈卡牌 -->
            <div class="card">
                <div class="card-content">
                    <div class="card-header">
                        <span class="card-level">7</span>
                        <span>❤️ 65</span>
                    </div>
                    <div class="card-icon">🏹</div>
                    <div class="card-name">月光射手</div>
                    <div class="card-stats">
                        <span class="stat-attack">⚔️ 90</span>
                        <span class="stat-speed">⚡ 18</span>
                    </div>
                    <div class="action-bar">
                        <div class="action-progress" style="width: 100%;"></div>
                    </div>
                    <div class="card-footer">
                        <span class="race-tag">精靈</span>
                        <span class="rarity-stars">⭐⭐⭐⭐</span>
                    </div>
                </div>
            </div>

            <!-- 龍族卡牌 -->
            <div class="card">
                <div class="card-content">
                    <div class="card-header">
                        <span class="card-level">8</span>
                        <span>❤️ 150</span>
                    </div>
                    <div class="card-icon">🔥</div>
                    <div class="card-name">火龍戰士</div>
                    <div class="card-stats">
                        <span class="stat-attack">⚔️ 130</span>
                        <span class="stat-speed">⚡ 15</span>
                    </div>
                    <div class="action-bar">
                        <div class="action-progress" style="width: 80%;"></div>
                    </div>
                    <div class="card-footer">
                        <span class="race-tag">龍族</span>
                        <span class="rarity-stars">⭐⭐⭐⭐⭐</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 手繪藝術風格 -->
    <div class="design-section handdrawn">
        <div class="design-title" style="color: #8b4513;">🎨 手繪藝術風格</div>
        <div class="card-container">
            <!-- 人族卡牌 -->
            <div class="card">
                <div class="card-content">
                    <div class="card-header">
                        <span class="card-level">5</span>
                        <span>❤️ 85</span>
                    </div>
                    <div class="card-icon">🛡️</div>
                    <div class="card-name">聖騎士</div>
                    <div class="card-stats">
                        <span class="stat-attack">⚔️ 75</span>
                        <span class="stat-speed">⚡ 12</span>
                    </div>
                    <div class="action-bar">
                        <div class="action-progress" style="width: 60%;"></div>
                    </div>
                    <div class="card-footer">
                        <span class="race-tag">人族</span>
                        <span class="rarity-stars">⭐⭐⭐</span>
                    </div>
                </div>
            </div>

            <!-- 精靈卡牌 -->
            <div class="card">
                <div class="card-content">
                    <div class="card-header">
                        <span class="card-level">7</span>
                        <span>❤️ 65</span>
                    </div>
                    <div class="card-icon">🏹</div>
                    <div class="card-name">月光射手</div>
                    <div class="card-stats">
                        <span class="stat-attack">⚔️ 90</span>
                        <span class="stat-speed">⚡ 18</span>
                    </div>
                    <div class="action-bar">
                        <div class="action-progress" style="width: 100%;"></div>
                    </div>
                    <div class="card-footer">
                        <span class="race-tag">精靈</span>
                        <span class="rarity-stars">⭐⭐⭐⭐</span>
                    </div>
                </div>
            </div>

            <!-- 龍族卡牌 -->
            <div class="card">
                <div class="card-content">
                    <div class="card-header">
                        <span class="card-level">8</span>
                        <span>❤️ 150</span>
                    </div>
                    <div class="card-icon">🔥</div>
                    <div class="card-name">火龍戰士</div>
                    <div class="card-stats">
                        <span class="stat-attack">⚔️ 130</span>
                        <span class="stat-speed">⚡ 15</span>
                    </div>
                    <div class="action-bar">
                        <div class="action-progress" style="width: 80%;"></div>
                    </div>
                    <div class="card-footer">
                        <span class="race-tag">龍族</span>
                        <span class="rarity-stars">⭐⭐⭐⭐⭐</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('click', function() {
                if (this.closest('.holographic')) {
                    this.style.transform = 'translateY(-8px) rotateY(25deg) rotateX(10deg)';
                    this.style.boxShadow = '0 20px 50px rgba(157, 78, 221, 0.7)';
                } else if (this.closest('.crystal')) {
                    this.style.transform = 'translateY(-6px) scale(1.1) rotateZ(5deg)';
                    this.style.boxShadow = '0 20px 50px rgba(251, 191, 36, 0.7)';
                } else if (this.closest('.handdrawn')) {
                    this.style.transform = 'translateY(-4px) rotate(5deg) scale(1.05)';
                    this.style.filter = 'drop-shadow(6px 6px 12px rgba(0, 0, 0, 0.3))';
                }
                
                setTimeout(() => {
                    this.style.transform = '';
                    this.style.boxShadow = '';
                    this.style.filter = '';
                }, 300);
            });
        });

        // 模擬行動條動畫
        function animateActionBars() {
            document.querySelectorAll('.action-progress').forEach(bar => {
                let currentWidth = parseInt(bar.style.width) || 0;
                let newWidth = (currentWidth + 15) % 115;
                bar.style.width = newWidth + '%';
            });
        }

        // 每3秒更新一次行動條
        setInterval(animateActionBars, 3000);

        // 添加閃爍效果給滿行動條的卡牌
        setInterval(() => {
            document.querySelectorAll('.action-progress').forEach(bar => {
                if (parseInt(bar.style.width) >= 100) {
                    const card = bar.closest('.card');
                    if (card.closest('.holographic')) {
                        card.style.borderColor = '#9d4edd';
                        setTimeout(() => {
                            card.style.borderColor = '#00ffff';
                        }, 500);
                    } else if (card.closest('.crystal')) {
                        card.style.boxShadow = '0 8px 25px rgba(251, 191, 36, 0.8), inset 0 2px 4px rgba(255, 255, 255, 0.4)';
                        setTimeout(() => {
                            card.style.boxShadow = '0 8px 25px rgba(251, 191, 36, 0.3), inset 0 2px 4px rgba(255, 255, 255, 0.2)';
                        }, 500);
                    } else if (card.closest('.handdrawn')) {
                        card.style.filter = 'drop-shadow(4px 4px 8px rgba(244, 162, 97, 0.8))';
                        setTimeout(() => {
                            card.style.filter = 'drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1))';
                        }, 500);
                    }
                }
            });
        }, 2000);
    </script>
</body>
</html>