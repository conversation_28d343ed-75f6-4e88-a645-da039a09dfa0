/**
 * 🔧 配置 Context
 * 
 * 提供配置數據給整個應用
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { configManager, GameConfigManager } from '../services/ConfigManager';

interface ConfigContextType {
  configManager: GameConfigManager;
  isLoading: boolean;
  isLoaded: boolean;
  error: string | null;
  stats: {
    cards: number;
    skills: number;
    stages: number;
    enemyDecks: number;
    drops: number;
    gacha: number;
    aiBehaviors: number;
    isLoaded: boolean;
  };
}

const ConfigContext = createContext<ConfigContextType | undefined>(undefined);

interface ConfigProviderProps {
  children: ReactNode;
}

export function ConfigProvider({ children }: ConfigProviderProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState(configManager.getConfigStats());

  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      console.log('🔧 ConfigProvider: 開始載入配置...');
      await configManager.loadConfigs();
      
      setStats(configManager.getConfigStats());
      setIsLoaded(true);
      console.log('✅ ConfigProvider: 配置載入完成');
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知錯誤';
      setError(errorMessage);
      console.error('❌ ConfigProvider: 配置載入失敗:', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const contextValue: ConfigContextType = {
    configManager,
    isLoading,
    isLoaded,
    error,
    stats
  };

  return (
    <ConfigContext.Provider value={contextValue}>
      {children}
    </ConfigContext.Provider>
  );
}

export function useConfig(): ConfigContextType {
  const context = useContext(ConfigContext);
  if (context === undefined) {
    throw new Error('useConfig must be used within a ConfigProvider');
  }
  return context;
}

export default ConfigContext;
