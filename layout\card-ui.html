<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Card UI Layout - PetingGame</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            min-height: 100vh;
        }

        .card-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
            max-width: 800px;
            margin: 0 auto;
        }

        /* 基礎卡牌樣式 */
        .card {
            width: 120px;
            height: 160px;
            background: linear-gradient(145deg, #ffffff 0%, #f0f0f0 100%);
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15), 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            border: 2px solid #e0e0e0;
        }

        .card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2), 0 4px 8px rgba(0,0,0,0.15);
        }

        .card:active {
            transform: scale(0.98);
        }

        /* 種族背景色彩 */
        .card.human { border-color: #3b82f6; background: linear-gradient(145deg, #eff6ff, #dbeafe); }
        .card.elf { border-color: #10b981; background: linear-gradient(145deg, #f0fdf4, #dcfce7); }
        .card.orc { border-color: #f59e0b; background: linear-gradient(145deg, #fffbeb, #fef3c7); }
        .card.dragon { border-color: #ef4444; background: linear-gradient(145deg, #fef2f2, #fecaca); }
        .card.angel { border-color: #8b5cf6; background: linear-gradient(145deg, #f5f3ff, #ede9fe); }
        .card.demon { border-color: #dc2626; background: linear-gradient(145deg, #fef2f2, #fee2e2); }

        /* 卡牌頂部信息 */
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            font-size: 12px;
            font-weight: bold;
        }

        .card-level {
            background: rgba(0,0,0,0.1);
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
        }

        .card-health {
            color: #dc2626;
            font-size: 14px;
        }

        /* 卡牌名稱區域 */
        .card-name {
            text-align: center;
            padding: 4px 8px;
            font-size: 14px;
            font-weight: bold;
            color: #1f2937;
            line-height: 1.2;
        }

        /* 卡牌屬性區域 */
        .card-stats {
            display: flex;
            justify-content: space-between;
            padding: 8px;
            font-size: 14px;
            font-weight: bold;
        }

        .stat-attack { color: #dc2626; }
        .stat-speed { color: #059669; }

        /* 行動條 */
        .action-bar {
            margin: 4px 8px;
            height: 8px;
            background: rgba(0,0,0,0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .action-progress {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #34d399);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* 卡牌底部標籤 */
        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 8px;
            margin-top: auto;
        }

        .race-tag {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            background: rgba(0,0,0,0.1);
        }

        .rarity-stars {
            font-size: 12px;
            color: #f59e0b;
        }

        /* 特殊效果 */
        .card.ready {
            animation: pulse 1.5s infinite;
            border-color: #10b981;
            box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
        }

        .card.selected {
            border-color: #3b82f6;
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* 響應式設計 */
        @media (max-width: 480px) {
            .card {
                width: 100px;
                height: 140px;
            }
            
            .card-name {
                font-size: 12px;
            }
            
            .card-stats {
                font-size: 12px;
            }
        }

        /* 示例標題 */
        .title {
            text-align: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .section {
            margin-bottom: 40px;
        }

        .section-title {
            color: white;
            font-size: 18px;
            margin-bottom: 15px;
            text-align: center;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <h1 class="title">🃏 PetingGame 卡牌 UI 設計</h1>

    <div class="section">
        <h2 class="section-title">不同種族卡牌</h2>
        <div class="card-container">
            <!-- 人族卡牌 -->
            <div class="card human">
                <div class="card-header">
                    <span class="card-level">Lv.5</span>
                    <span class="card-health">❤️ 85</span>
                </div>
                <div class="card-name">🛡️ 聖騎士</div>
                <div class="card-stats">
                    <span class="stat-attack">⚔️ 75</span>
                    <span class="stat-speed">⚡ 12</span>
                </div>
                <div class="action-bar">
                    <div class="action-progress" style="width: 60%;"></div>
                </div>
                <div class="card-footer">
                    <span class="race-tag">🔵 人族</span>
                    <span class="rarity-stars">⭐⭐⭐</span>
                </div>
            </div>

            <!-- 精靈卡牌 -->
            <div class="card elf ready">
                <div class="card-header">
                    <span class="card-level">Lv.7</span>
                    <span class="card-health">❤️ 65</span>
                </div>
                <div class="card-name">🏹 月光射手</div>
                <div class="card-stats">
                    <span class="stat-attack">⚔️ 90</span>
                    <span class="stat-speed">⚡ 18</span>
                </div>
                <div class="action-bar">
                    <div class="action-progress" style="width: 100%;"></div>
                </div>
                <div class="card-footer">
                    <span class="race-tag">🟢 精靈</span>
                    <span class="rarity-stars">⭐⭐⭐⭐</span>
                </div>
            </div>

            <!-- 獸人卡牌 -->
            <div class="card orc">
                <div class="card-header">
                    <span class="card-level">Lv.4</span>
                    <span class="card-health">❤️ 120</span>
                </div>
                <div class="card-name">🪓 狂戰士</div>
                <div class="card-stats">
                    <span class="stat-attack">⚔️ 110</span>
                    <span class="stat-speed">⚡ 8</span>
                </div>
                <div class="action-bar">
                    <div class="action-progress" style="width: 30%;"></div>
                </div>
                <div class="card-footer">
                    <span class="race-tag">🟠 獸人</span>
                    <span class="rarity-stars">⭐⭐</span>
                </div>
            </div>

            <!-- 龍族卡牌 -->
            <div class="card dragon selected">
                <div class="card-header">
                    <span class="card-level">Lv.8</span>
                    <span class="card-health">❤️ 150</span>
                </div>
                <div class="card-name">🔥 火龍戰士</div>
                <div class="card-stats">
                    <span class="stat-attack">⚔️ 130</span>
                    <span class="stat-speed">⚡ 15</span>
                </div>
                <div class="action-bar">
                    <div class="action-progress" style="width: 80%;"></div>
                </div>
                <div class="card-footer">
                    <span class="race-tag">🔴 龍族</span>
                    <span class="rarity-stars">⭐⭐⭐⭐⭐</span>
                </div>
            </div>

            <!-- 天使卡牌 -->
            <div class="card angel">
                <div class="card-header">
                    <span class="card-level">Lv.6</span>
                    <span class="card-health">❤️ 95</span>
                </div>
                <div class="card-name">😇 治療天使</div>
                <div class="card-stats">
                    <span class="stat-attack">⚔️ 60</span>
                    <span class="stat-speed">⚡ 14</span>
                </div>
                <div class="action-bar">
                    <div class="action-progress" style="width: 45%;"></div>
                </div>
                <div class="card-footer">
                    <span class="race-tag">🟣 天使</span>
                    <span class="rarity-stars">⭐⭐⭐</span>
                </div>
            </div>

            <!-- 惡魔卡牌 -->
            <div class="card demon">
                <div class="card-header">
                    <span class="card-level">Lv.5</span>
                    <span class="card-health">❤️ 80</span>
                </div>
                <div class="card-name">👹 暗影惡魔</div>
                <div class="card-stats">
                    <span class="stat-attack">⚔️ 95</span>
                    <span class="stat-speed">⚡ 16</span>
                </div>
                <div class="action-bar">
                    <div class="action-progress" style="width: 70%;"></div>
                </div>
                <div class="card-footer">
                    <span class="race-tag">⚫ 惡魔</span>
                    <span class="rarity-stars">⭐⭐⭐⭐</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 簡單的點擊交互效果
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('click', function() {
                // 移除其他選中狀態
                document.querySelectorAll('.card').forEach(c => c.classList.remove('selected'));
                // 添加選中狀態
                this.classList.toggle('selected');
            });
        });

        // 模擬行動條動畫
        function animateActionBars() {
            document.querySelectorAll('.action-progress').forEach(bar => {
                let currentWidth = parseInt(bar.style.width) || 0;
                let newWidth = (currentWidth + 10) % 110;
                bar.style.width = newWidth + '%';
                
                // 行動條滿時添加ready效果
                if (newWidth >= 100) {
                    bar.closest('.card').classList.add('ready');
                } else {
                    bar.closest('.card').classList.remove('ready');
                }
            });
        }

        // 每2秒更新一次行動條
        setInterval(animateActionBars, 2000);
    </script>
</body>
</html>