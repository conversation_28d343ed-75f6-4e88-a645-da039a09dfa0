/**
 * 🎴 抽卡系統類型定義
 * 
 * 定義抽卡系統的所有相關類型和接口
 * 基於 docs/game_design.md 中的抽卡系統設計
 */

import { Card, CardRarity, PlayerData } from './index';
import { GachaConfig, GachaCardPool, GachaGuaranteeRule } from './config';

// ==================== 抽卡核心接口 ====================

/**
 * 抽卡請求接口
 */
export interface GachaRequest {
  poolId: string;                       // 抽卡池ID
  drawCount: number;                    // 抽取數量 (1 或 10)
  currencyType: 'gold' | 'diamond';     // 貨幣類型
  cost: number;                         // 消耗數量
  playerId: string;                     // 玩家ID
  timestamp: Date;                      // 請求時間
}

/**
 * 抽卡結果接口
 */
export interface GachaResult {
  requestId: string;                    // 請求ID
  success: boolean;                     // 是否成功
  cards: GachaCardResult[];             // 抽到的卡牌
  totalCost: number;                    // 總消耗
  currencyType: 'gold' | 'diamond';     // 貨幣類型
  guaranteeTriggered: boolean;          // 是否觸發保底
  guaranteeType?: CardRarity;           // 保底類型
  newCards: Card[];                     // 新獲得的卡牌
  duplicateCards: GachaDuplicateInfo[]; // 重複卡牌信息
  timestamp: Date;                      // 結果時間
  animationSequence: GachaAnimation[];  // 動畫序列
}

/**
 * 抽卡卡牌結果
 */
export interface GachaCardResult {
  card: Card;                           // 卡牌數據
  isNew: boolean;                       // 是否新卡
  isGuarantee: boolean;                 // 是否保底
  isRateUp: boolean;                    // 是否提升機率
  probability: number;                  // 實際抽中機率
  position: number;                     // 在結果中的位置
}

/**
 * 重複卡牌處理信息
 */
export interface GachaDuplicateInfo {
  cardId: string;                       // 卡牌ID
  duplicateCount: number;               // 重複數量
  conversionType: 'experience' | 'currency' | 'fragment'; // 轉換類型
  conversionValue: number;              // 轉換數值
  conversionCurrency?: 'gold' | 'diamond'; // 轉換貨幣類型
}

// ==================== 抽卡動畫接口 ====================

/**
 * 抽卡動畫類型
 */
export enum GachaAnimationType {
  CARD_PACK_OPEN = 'cardPackOpen',      // 卡包開啟
  LIGHT_BURST = 'lightBurst',           // 光效爆發
  CARD_FLIP = 'cardFlip',               // 卡牌翻轉
  RARITY_EFFECT = 'rarityEffect',       // 稀有度特效
  RESULT_DISPLAY = 'resultDisplay'      // 結果顯示
}

/**
 * 抽卡動畫配置
 */
export interface GachaAnimation {
  id: string;                           // 動畫ID
  type: GachaAnimationType;             // 動畫類型
  duration: number;                     // 持續時間(毫秒)
  delay: number;                        // 延遲時間(毫秒)
  cardRarity?: CardRarity;              // 卡牌稀有度
  effectIntensity: number;              // 特效強度 (1-5)
  soundEffect?: string;                 // 音效ID
  particleEffect?: string;              // 粒子效果ID
}

/**
 * 稀有度特效配置
 */
export interface RarityEffectConfig {
  rarity: CardRarity;                   // 稀有度
  lightColor: string;                   // 光效顏色
  particleCount: number;                // 粒子數量
  animationDuration: number;            // 動畫時長
  soundEffect: string;                  // 音效
  backgroundEffect: string;             // 背景特效
  textEffect: string;                   // 文字特效
}

// ==================== 抽卡池管理接口 ====================

/**
 * 抽卡池狀態
 */
export enum GachaPoolStatus {
  ACTIVE = 'active',                    // 啟用中
  INACTIVE = 'inactive',                // 未啟用
  EXPIRED = 'expired',                  // 已過期
  COMING_SOON = 'comingSoon'            // 即將開放
}

/**
 * 抽卡池信息
 */
export interface GachaPoolInfo {
  config: GachaConfig;                  // 池子配置
  status: GachaPoolStatus;              // 池子狀態
  remainingTime?: number;               // 剩餘時間(秒)
  totalDraws: number;                   // 總抽取次數
  featuredCards: Card[];                // 精選卡牌
  rateUpCards: Card[];                  // 提升機率卡牌
  guaranteeProgress: GachaGuaranteeProgress[]; // 保底進度
}

/**
 * 保底進度
 */
export interface GachaGuaranteeProgress {
  rule: GachaGuaranteeRule;             // 保底規則
  currentCount: number;                 // 當前計數
  remainingCount: number;               // 剩餘計數
  isTriggered: boolean;                 // 是否已觸發
  lastTriggeredAt?: Date;               // 最後觸發時間
}

// ==================== 抽卡歷史接口 ====================

/**
 * 抽卡歷史記錄
 */
export interface GachaHistory {
  id: string;                           // 記錄ID
  playerId: string;                     // 玩家ID
  poolId: string;                       // 抽卡池ID
  poolName: string;                     // 抽卡池名稱
  drawCount: number;                    // 抽取數量
  cost: number;                         // 消耗數量
  currencyType: 'gold' | 'diamond';     // 貨幣類型
  cards: GachaHistoryCard[];            // 抽到的卡牌
  guaranteeTriggered: boolean;          // 是否觸發保底
  timestamp: Date;                      // 抽卡時間
}

/**
 * 抽卡歷史卡牌
 */
export interface GachaHistoryCard {
  cardId: string;                       // 卡牌ID
  cardName: string;                     // 卡牌名稱
  rarity: CardRarity;                   // 稀有度
  isNew: boolean;                       // 是否新卡
  isGuarantee: boolean;                 // 是否保底
}

/**
 * 抽卡統計
 */
export interface GachaStatistics {
  totalDraws: number;                   // 總抽取次數
  totalCost: number;                    // 總消耗
  goldSpent: number;                    // 消耗金幣
  diamondsSpent: number;                // 消耗鑽石
  cardsByRarity: Record<CardRarity, number>; // 各稀有度卡牌數量
  guaranteeTriggered: number;           // 保底觸發次數
  newCardsObtained: number;             // 新卡獲得數量
  duplicatesObtained: number;           // 重複卡獲得數量
  averageCostPerCard: number;           // 每張卡平均成本
  luckiestDraw: GachaHistory;           // 最幸運的抽卡
  mostExpensiveCard: GachaHistoryCard;  // 最昂貴的卡牌
}

// ==================== 抽卡管理器接口 ====================

/**
 * 抽卡管理器接口
 */
export interface GachaManager {
  // 抽卡池管理
  getAvailablePools(): GachaPoolInfo[];
  getPoolInfo(poolId: string): GachaPoolInfo | null;
  isPoolActive(poolId: string): boolean;
  
  // 抽卡執行
  performDraw(request: GachaRequest): Promise<GachaResult>;
  calculateDrawCost(poolId: string, drawCount: number): number;
  canAffordDraw(playerData: PlayerData, poolId: string, drawCount: number): boolean;
  
  // 機率計算
  calculateCardProbabilities(poolId: string): Map<string, number>;
  getGuaranteeProgress(playerId: string, poolId: string): GachaGuaranteeProgress[];
  updateGuaranteeProgress(playerId: string, poolId: string, result: GachaResult): void;
  
  // 歷史管理
  getDrawHistory(playerId: string, limit?: number): GachaHistory[];
  getDrawStatistics(playerId: string): GachaStatistics;
  saveDrawHistory(history: GachaHistory): void;
  
  // 重複卡處理
  processDuplicateCards(cards: Card[], playerData: PlayerData): GachaDuplicateInfo[];
  convertDuplicateCard(cardId: string, count: number): GachaDuplicateInfo;
}

// ==================== 抽卡事件接口 ====================

/**
 * 抽卡事件類型
 */
export enum GachaEventType {
  DRAW_STARTED = 'drawStarted',         // 抽卡開始
  DRAW_COMPLETED = 'drawCompleted',     // 抽卡完成
  ANIMATION_STARTED = 'animationStarted', // 動畫開始
  ANIMATION_COMPLETED = 'animationCompleted', // 動畫完成
  CARD_REVEALED = 'cardRevealed',       // 卡牌揭示
  GUARANTEE_TRIGGERED = 'guaranteeTriggered', // 保底觸發
  NEW_CARD_OBTAINED = 'newCardObtained', // 新卡獲得
  DUPLICATE_PROCESSED = 'duplicateProcessed', // 重複卡處理
  POOL_STATUS_CHANGED = 'poolStatusChanged' // 池子狀態變更
}

/**
 * 抽卡事件接口
 */
export interface GachaEvent {
  id: string;                           // 事件ID
  type: GachaEventType;                 // 事件類型
  timestamp: Date;                      // 時間戳
  playerId: string;                     // 玩家ID
  poolId?: string;                      // 抽卡池ID
  data: any;                            // 事件數據
}

// ==================== 抽卡UI接口 ====================

/**
 * 抽卡UI狀態
 */
export enum GachaUIState {
  IDLE = 'idle',                        // 空閒
  DRAWING = 'drawing',                  // 抽卡中
  ANIMATING = 'animating',              // 動畫中
  SHOWING_RESULTS = 'showingResults',   // 顯示結果
  PROCESSING = 'processing'             // 處理中
}

/**
 * 抽卡UI配置
 */
export interface GachaUIConfig {
  enableAnimations: boolean;            // 啟用動畫
  animationSpeed: number;               // 動畫速度
  showProbabilities: boolean;           // 顯示機率
  showHistory: boolean;                 // 顯示歷史
  enableSoundEffects: boolean;          // 啟用音效
  enableHapticFeedback: boolean;        // 啟用觸覺反饋
  skipAnimationOnMultipleDraw: boolean; // 多抽時跳過動畫
  autoCloseResultsAfter: number;        // 自動關閉結果時間(秒)
}

/**
 * 抽卡結果展示配置
 */
export interface GachaResultDisplayConfig {
  showCardDetails: boolean;             // 顯示卡牌詳情
  showNewCardBadge: boolean;            // 顯示新卡徽章
  showRarityEffects: boolean;           // 顯示稀有度特效
  groupByRarity: boolean;               // 按稀有度分組
  sortOrder: 'rarity' | 'name' | 'obtained'; // 排序方式
  maxCardsPerRow: number;               // 每行最大卡牌數
}
