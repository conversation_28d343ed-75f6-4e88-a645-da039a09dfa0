<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🃏 PetingGame - Types 簡化架構圖</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@9.4.3/dist/mermaid.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
        }

        .diagram-section {
            margin: 40px 0;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .diagram-title {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            text-align: center;
        }

        .mermaid-container {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            overflow-x: auto;
        }

        .tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #ecf0f1;
        }

        .tab {
            padding: 12px 24px;
            background: none;
            border: none;
            font-size: 1.1em;
            cursor: pointer;
            color: #7f8c8d;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab:hover {
            color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .info-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .info-card h3 {
            margin-bottom: 10px;
            font-size: 1.3em;
        }

        .info-card p {
            opacity: 0.9;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🃏 PetingGame Types 架構</h1>
            <p>TypeScript 類型系統簡化視圖</p>
        </div>

        <!-- Tab Navigation -->
        <div class="tabs">
            <button class="tab active" onclick="showTab('overview')">📊 總覽</button>
            <button class="tab" onclick="showTab('core')">🎯 核心類型</button>
            <button class="tab" onclick="showTab('systems')">⚙️ 系統模塊</button>
            <button class="tab" onclick="showTab('relationships')">🔗 關係圖</button>
        </div>

        <!-- Overview Tab -->
        <div id="overview" class="tab-content active">
            <div class="diagram-section">
                <h2 class="diagram-title">🏗️ 整體架構概覽</h2>
                <div class="mermaid-container">
                    <div style="text-align: center; padding: 40px; background: #f8f9fa; border-radius: 10px;">
                        <h3 style="color: #2c3e50; margin-bottom: 30px;">🏗️ 整體架構概覽</h3>

                        <!-- Core Types -->
                        <div style="display: inline-block; margin: 20px; padding: 20px; background: #e74c3c; color: white; border-radius: 10px; min-width: 200px;">
                            <h4>🎯 Core Types</h4>
                            <div style="margin-top: 10px; font-size: 0.9em;">
                                Card → BattleCard<br>
                                Team → PlayerData
                            </div>
                        </div>

                        <div style="display: inline-block; margin: 0 20px; font-size: 2em; color: #667eea;">→</div>

                        <!-- System Modules -->
                        <div style="display: inline-block; margin: 20px; padding: 20px; background: #f39c12; color: white; border-radius: 10px; min-width: 200px;">
                            <h4>⚙️ System Modules</h4>
                            <div style="margin-top: 10px; font-size: 0.9em;">
                                Config → Battle<br>
                                Gacha → UI
                            </div>
                        </div>

                        <div style="display: inline-block; margin: 0 20px; font-size: 2em; color: #667eea;">→</div>

                        <!-- Utilities -->
                        <div style="display: inline-block; margin: 20px; padding: 20px; background: #2ecc71; color: white; border-radius: 10px; min-width: 200px;">
                            <h4>🛠️ Utilities</h4>
                            <div style="margin-top: 10px; font-size: 0.9em;">
                                Utils ↔ All<br>
                                Constants ↔ All
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Core Types Tab -->
        <div id="core" class="tab-content">
            <div class="diagram-section">
                <h2 class="diagram-title">🎯 核心類型結構</h2>
                <div class="mermaid-container">
                    <div style="background: #f8f9fa; padding: 30px; border-radius: 10px;">
                        <h3 style="color: #2c3e50; text-align: center; margin-bottom: 30px;">🎯 核心類型結構</h3>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                            <!-- Card Class -->
                            <div style="background: white; padding: 20px; border-radius: 10px; border-left: 5px solid #e74c3c;">
                                <h4 style="color: #e74c3c; margin-bottom: 15px;">📄 Card</h4>
                                <div style="font-family: monospace; font-size: 0.9em; line-height: 1.6;">
                                    + id: string<br>
                                    + name: string<br>
                                    + race: CardRace<br>
                                    + rarity: CardRarity<br>
                                    + level: number<br>
                                    + stats: CardStats<br>
                                    + battleTags: BattleTag[]<br>
                                    + skills: CardSkill[]
                                </div>
                            </div>

                            <!-- BattleCard Class -->
                            <div style="background: white; padding: 20px; border-radius: 10px; border-left: 5px solid #e67e22;">
                                <h4 style="color: #e67e22; margin-bottom: 15px;">⚔️ BattleCard extends Card</h4>
                                <div style="font-family: monospace; font-size: 0.9em; line-height: 1.6;">
                                    + currentHealth: number<br>
                                    + actionBar: number<br>
                                    + buffs: StatusEffect[]<br>
                                    + debuffs: StatusEffect[]<br>
                                    + isAlive: boolean<br>
                                    + position: number<br>
                                    + team: 'player' | 'enemy'
                                </div>
                            </div>

                            <!-- Team Class -->
                            <div style="background: white; padding: 20px; border-radius: 10px; border-left: 5px solid #3498db;">
                                <h4 style="color: #3498db; margin-bottom: 15px;">👥 Team</h4>
                                <div style="font-family: monospace; font-size: 0.9em; line-height: 1.6;">
                                    + id: string<br>
                                    + name: string<br>
                                    + cards: Card[]<br>
                                    + totalPower: number<br>
                                    + createdAt: Date
                                </div>
                            </div>

                            <!-- PlayerData Class -->
                            <div style="background: white; padding: 20px; border-radius: 10px; border-left: 5px solid #9b59b6;">
                                <h4 style="color: #9b59b6; margin-bottom: 15px;">👤 PlayerData</h4>
                                <div style="font-family: monospace; font-size: 0.9em; line-height: 1.6;">
                                    + id: string<br>
                                    + level: number<br>
                                    + gold: number<br>
                                    + diamonds: number<br>
                                    + ownedCards: Card[]<br>
                                    + teams: Team[]<br>
                                    + settings: GameSettings
                                </div>
                            </div>
                        </div>

                        <!-- Enums -->
                        <div style="margin-top: 30px; display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div style="background: white; padding: 20px; border-radius: 10px; border-left: 5px solid #f39c12;">
                                <h4 style="color: #f39c12; margin-bottom: 15px;">🌟 CardRarity</h4>
                                <div style="font-family: monospace; font-size: 0.9em;">
                                    COMMON (1⭐)<br>
                                    RARE (2⭐)<br>
                                    EPIC (3⭐)<br>
                                    LEGENDARY (4⭐)<br>
                                    MYTHIC (5⭐)
                                </div>
                            </div>

                            <div style="background: white; padding: 20px; border-radius: 10px; border-left: 5px solid #2ecc71;">
                                <h4 style="color: #2ecc71; margin-bottom: 15px;">🏰 CardRace</h4>
                                <div style="font-family: monospace; font-size: 0.9em;">
                                    HUMAN 👤<br>
                                    ELF 🧝<br>
                                    ORC 👹<br>
                                    DRAGON 🐉<br>
                                    ANGEL 👼<br>
                                    DEMON 😈
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Systems Tab -->
        <div id="systems" class="tab-content">
            <div class="diagram-section">
                <h2 class="diagram-title">⚙️ 系統模塊交互</h2>
                <div class="mermaid-container">
                    <div class="mermaid">
                        graph LR
                            subgraph ConfigSystem ["🔧 Config System"]
                                A[CardConfig]
                                B[SkillConfig]
                                C[StageConfig]
                                D[GachaConfig]
                            end

                            subgraph BattleSystem ["⚔️ Battle System"]
                                E[Battle]
                                F[BattleAction]
                                G[AIDecision]
                                H[BattleManager]
                            end

                            subgraph GachaSystem ["🎴 Gacha System"]
                                I[GachaRequest]
                                J[GachaResult]
                                K[GachaManager]
                                L[GachaHistory]
                            end

                            subgraph UISystem ["🎨 UI System"]
                                M[Theme]
                                N[ComponentProps]
                                O[AnimationConfig]
                                P[ResponsiveConfig]
                            end

                            A --> E
                            B --> F
                            C --> E
                            D --> I
                            E --> M
                            F --> N
                            I --> J
                            J --> L

                            style A fill:#f39c12,color:#fff
                            style B fill:#f39c12,color:#fff
                            style C fill:#f39c12,color:#fff
                            style D fill:#f39c12,color:#fff
                            style E fill:#e67e22,color:#fff
                            style F fill:#e67e22,color:#fff
                            style G fill:#e67e22,color:#fff
                            style H fill:#e67e22,color:#fff
                            style I fill:#3498db,color:#fff
                            style J fill:#3498db,color:#fff
                            style K fill:#3498db,color:#fff
                            style L fill:#3498db,color:#fff
                            style M fill:#9b59b6,color:#fff
                            style N fill:#9b59b6,color:#fff
                            style O fill:#9b59b6,color:#fff
                            style P fill:#9b59b6,color:#fff
                    </div>
                </div>
            </div>
        </div>

        <!-- Relationships Tab -->
        <div id="relationships" class="tab-content">
            <div class="diagram-section">
                <h2 class="diagram-title">🔗 類型依賴關係</h2>
                <div class="mermaid-container">
                    <div class="mermaid">
                        flowchart TD
                            A["index.ts 核心類型定義"] --> B["config.ts 配置系統類型"]
                            A --> C["battle.ts 戰鬥系統類型"]
                            A --> D["gacha.ts 抽卡系統類型"]
                            A --> E["ui.ts UI系統類型"]

                            B --> C
                            B --> D
                            C --> E
                            D --> E

                            F["utils.ts 工具函數"] -.-> A
                            F -.-> B
                            F -.-> C
                            F -.-> D
                            F -.-> E

                            G["constants.ts 常量定義"] -.-> A
                            G -.-> B
                            G -.-> C
                            G -.-> D
                            G -.-> E

                            style A fill:#e74c3c,stroke:#c0392b,color:#fff
                            style B fill:#f39c12,stroke:#e67e22,color:#fff
                            style C fill:#e67e22,stroke:#d35400,color:#fff
                            style D fill:#3498db,stroke:#2980b9,color:#fff
                            style E fill:#9b59b6,stroke:#8e44ad,color:#fff
                            style F fill:#2ecc71,stroke:#27ae60,color:#fff
                            style G fill:#2ecc71,stroke:#27ae60,color:#fff
                    </div>
                </div>
            </div>
        </div>

        <!-- Info Cards -->
        <div class="info-grid">
            <div class="info-card">
                <h3>🎯 核心設計</h3>
                <p>基於遊戲設計文檔，建立完整的類型安全系統，確保代碼質量和可維護性。</p>
            </div>
            <div class="info-card">
                <h3>🔧 模塊化</h3>
                <p>按功能分離類型定義，便於管理和擴展，支持漸進式開發。</p>
            </div>
            <div class="info-card">
                <h3>🛡️ 類型安全</h3>
                <p>提供運行時類型檢查和驗證工具，防止類型錯誤和數據異常。</p>
            </div>
            <div class="info-card">
                <h3>📚 文檔完善</h3>
                <p>詳細的類型註釋和使用示例，降低學習成本，提高開發效率。</p>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            },
            classDiagram: {
                useMaxWidth: true
            }
        });

        // 手動渲染所有 Mermaid 圖表
        function renderMermaidDiagrams() {
            const mermaidElements = document.querySelectorAll('.mermaid');
            mermaidElements.forEach((element, index) => {
                const graphDefinition = element.textContent.trim();
                if (graphDefinition) {
                    try {
                        mermaid.render(`mermaid-${index}`, graphDefinition, (svgCode) => {
                            element.innerHTML = svgCode;
                        });
                    } catch (error) {
                        console.error('Mermaid rendering error:', error);
                        element.innerHTML = `<div style="color: red; padding: 20px;">圖表渲染錯誤: ${error.message}</div>`;
                    }
                }
            });
        }

        // Tab 切換功能
        function showTab(tabName) {
            // 隱藏所有 tab content
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有 tab 的 active 狀態
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 顯示選中的 tab content
            document.getElementById(tabName).classList.add('active');

            // 添加選中 tab 的 active 狀態
            event.target.classList.add('active');

            // 重新渲染當前標籤頁的 Mermaid 圖表
            setTimeout(() => {
                const activeTab = document.getElementById(tabName);
                const mermaidElements = activeTab.querySelectorAll('.mermaid');
                mermaidElements.forEach((element, index) => {
                    const graphDefinition = element.textContent.trim();
                    if (graphDefinition && !element.querySelector('svg')) {
                        try {
                            mermaid.render(`mermaid-${tabName}-${index}`, graphDefinition, (svgCode) => {
                                element.innerHTML = svgCode;
                            });
                        } catch (error) {
                            console.error('Mermaid rendering error:', error);
                            element.innerHTML = `<div style="color: red; padding: 20px;">圖表渲染錯誤: ${error.message}</div>`;
                        }
                    }
                });
            }, 100);
        }

        // 頁面載入完成後的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始渲染第一個標籤頁的圖表
            setTimeout(() => {
                renderMermaidDiagrams();
            }, 500);

            // 添加一些互動效果
            const infoCards = document.querySelectorAll('.info-card');
            infoCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                    this.style.boxShadow = '0 15px 40px rgba(0, 0, 0, 0.2)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = 'none';
                });
            });
        });
    </script>
</body>
</html>
