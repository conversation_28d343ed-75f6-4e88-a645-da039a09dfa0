# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 項目概述

這是一個混合架構的卡牌戰鬥遊戲項目，包含兩個主要部分：
- **BattleCardExpo/**: React Native + Expo 手機應用（主要開發項目）
- **layout/**: HTML/CSS UI 設計原型和參考

## 開發指令

### Expo React Native 應用 (BattleCardExpo/)
```bash
# 安裝依賴
npm install

# 開發運行
npm start
# 或
npx expo start

# 平台特定運行
npm run android  # Android 模擬器
npm run ios      # iOS 模擬器  
npm run web      # Web 瀏覽器

# 代碼檢查
npm run lint

# 重置項目（清理示例代碼）
npm run reset-project
```

## 架構設計

### Expo React Native 應用架構
- **技術棧**: React Native 0.79, Expo SDK 53, TypeScript
- **導航**: Expo Router (file-based routing)
- **UI 組件**: 自定義主題化組件 (ThemedView, ThemedText)
- **動畫**: React Native Reanimated
- **圖標**: Expo Vector Icons
- **狀態管理**: React hooks (暫無全域狀態管理)

### 項目結構
```
BattleCardExpo/
├── app/                 # 主要頁面路由
│   ├── (tabs)/         # 底部標籤導航頁面
│   │   ├── index.tsx   # 主頁
│   │   └── explore.tsx # 探索頁
│   └── _layout.tsx     # 根布局
├── components/         # 可重用組件
│   ├── ThemedView.tsx  # 主題化視圖
│   ├── ThemedText.tsx  # 主題化文字
│   └── ui/             # UI 基礎組件
├── constants/          # 常量配置
├── hooks/              # 自定義 hooks
└── docs/              # 遊戲設計文檔
    └── game_design.md # 完整遊戲設計規格
```

### 設計系統
- **主題**: 支持明暗主題切換
- **顏色**: 基於系統主題的動態顏色
- **字體**: SpaceMono (等寬字體) + 系統字體
- **響應式**: 針對手機直向模式優化 (9:16)
- **觸控**: 最小 48dp 觸控區域

## 遊戲設計參考

### 核心概念
基於 `docs/game_design.md` 的設計，這是一個自動戰鬥策略卡牌收集遊戲：
- **全自動戰鬥**: 戰鬥完全自動進行
- **速度驅動**: 基於卡牌速度屬性的行動順序
- **卡牌收集**: 抽卡系統和卡牌強化
- **手機優化**: 專為5-7吋螢幕設計

### UI 設計參考
`layout/` 目錄包含 HTML/CSS 設計原型：
- `main-menu.html`: 主界面佈局
- `battle-ui.html`: 戰鬥界面設計
- `card-ui.html`: 卡牌組件設計
- `gacha-ui.html`: 抽卡界面設計

## 開發規範

### Bug 修復方法論
在進行 Bug 修復時，必須需要進行深入思考和全面分析，對於每個 Bug，至少需要詢問自己以下問題：

1. **問題描述**: 問題是什麼？詳細描述 Bug 的表現
2. **預期目標**: 目標是什麼？預期的正確結果是什麼？
3. **歷史分析**: 如果不是第一次修復此 Bug，之前遺漏了什麼？
4. **線索搜尋**: 在上下文或腳本中，可以找到哪些線索幫助實現任務？
5. **根因分析**: 可能導致 Bug 的原因是什麼？列出所有可能性
6. **深度檢查**: 如果當前分析不是主要原因，是否需要尋找更多線索或代碼？
7. **解決驗證**: 已做出的更改是什麼？為什麼這些更改可以修復 Bug？

### 代碼風格
- 遵循 TypeScript 嚴格模式
- 使用 ESLint 進行代碼檢查
- 組件使用函數式組件 + hooks
- 檔名使用 PascalCase (組件) 或 camelCase (工具)

### 開發流程
1. 開發前先運行 `npm install` 確保依賴最新
2. 使用 `npm run lint` 檢查代碼風格
3. 功能完成後在多個平台測試 (iOS/Android/Web)
4. 參考設計文檔確保符合遊戲設計理念