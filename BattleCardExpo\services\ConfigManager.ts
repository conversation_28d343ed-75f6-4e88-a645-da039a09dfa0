/**
 * 🔧 配置管理器服務
 * 
 * 負責載入和管理所有遊戲配置數據
 * 對應 docs/game_design.md 中的配置系統設計
 */

import * as FileSystem from 'expo-file-system';
import { Asset } from 'expo-asset';
import {
  ConfigManager,
  CardConfig,
  SkillConfig,
  StageConfig,
  EnemyDeckConfig,
  DropConfig,
  GachaConfig,
  AIBehaviorConfig,
  CSVParseResult,
  ConfigValidationResult
} from '../types/config';

/**
 * 配置管理器實現
 */
export class GameConfigManager implements ConfigManager {
  // 配置數據存儲
  cards = new Map<string, CardConfig>();
  skills = new Map<string, SkillConfig>();
  stages = new Map<string, StageConfig>();
  enemyDecks = new Map<string, EnemyDeckConfig>();
  drops = new Map<string, DropConfig>();
  gacha = new Map<string, GachaConfig>();
  aiBehaviors = new Map<string, AIBehaviorConfig>();

  private isLoaded = false;
  private loadingPromise: Promise<void> | null = null;

  /**
   * 載入所有配置文件
   */
  async loadConfigs(): Promise<void> {
    if (this.isLoaded) {
      return;
    }

    if (this.loadingPromise) {
      return this.loadingPromise;
    }

    this.loadingPromise = this._loadConfigsInternal();
    return this.loadingPromise;
  }

  private async _loadConfigsInternal(): Promise<void> {
    try {
      console.log('🔧 開始載入遊戲配置...');

      // 並行載入所有配置文件
      const [
        cardsResult,
        skillsResult,
        stagesResult,
        enemyDecksResult,
        dropsResult,
        gachaResult,
        aiBehaviorsResult
      ] = await Promise.all([
        this.loadCSVConfig<CardConfig>('CardConfig.csv'),
        this.loadCSVConfig<SkillConfig>('SkillConfig.csv'),
        this.loadCSVConfig<StageConfig>('StageConfig.csv'),
        this.loadCSVConfig<EnemyDeckConfig>('EnemyDeckConfig.csv'),
        this.loadCSVConfig<DropConfig>('DropConfig.csv'),
        this.loadCSVConfig<GachaConfig>('GachaConfig.csv'),
        this.loadCSVConfig<AIBehaviorConfig>('AIBehaviorConfig.csv')
      ]);

      // 處理載入結果
      this.processConfigResults('cards', cardsResult, this.cards);
      this.processConfigResults('skills', skillsResult, this.skills);
      this.processConfigResults('stages', stagesResult, this.stages);
      this.processConfigResults('enemyDecks', enemyDecksResult, this.enemyDecks);
      this.processConfigResults('drops', dropsResult, this.drops);
      this.processConfigResults('gacha', gachaResult, this.gacha);
      this.processConfigResults('aiBehaviors', aiBehaviorsResult, this.aiBehaviors);

      // 驗證配置完整性
      const validationResult = this.validateConfigs();
      if (!validationResult.isValid) {
        console.warn('⚠️ 配置驗證發現問題:', validationResult.errors);
      }

      this.isLoaded = true;
      console.log('✅ 配置載入完成');
      console.log(`📊 載入統計: 卡牌${this.cards.size}, 技能${this.skills.size}, 關卡${this.stages.size}`);

    } catch (error) {
      console.error('❌ 配置載入失敗:', error);
      throw new Error(`配置載入失敗: ${error}`);
    }
  }

  /**
   * 載入單個 CSV 配置文件
   */
  private async loadCSVConfig<T>(filename: string): Promise<CSVParseResult<T>> {
    try {
      // 嘗試直接從 assets 讀取
      let csvContent: string;

      try {
        // 在 Expo 中，我們需要使用 require 來獲取 asset
        const assetModule = require(`../config/${filename}`);
        const asset = Asset.fromModule(assetModule);
        await asset.downloadAsync();

        if (asset.localUri) {
          csvContent = await FileSystem.readAsStringAsync(asset.localUri);
        } else {
          throw new Error('無法獲取本地路徑');
        }
      } catch (assetError) {
        // 如果 asset 載入失敗，嘗試使用 FileSystem 直接讀取
        console.warn(`Asset 載入失敗，嘗試直接讀取: ${assetError}`);

        // 創建示例數據作為後備
        csvContent = this.getDefaultCSVContent(filename);
      }

      // 解析 CSV
      const parseResult = this.parseCSV<T>(csvContent, filename);

      return parseResult;

    } catch (error) {
      console.error(`❌ 載入 ${filename} 失敗:`, error);

      // 返回默認數據
      const defaultContent = this.getDefaultCSVContent(filename);
      return this.parseCSV<T>(defaultContent, filename);
    }
  }

  /**
   * 獲取默認 CSV 內容（用於測試）
   */
  private getDefaultCSVContent(filename: string): string {
    switch (filename) {
      case 'CardConfig.csv':
        return `id,name,race,rarity,baseAttack,baseMagicAttack,baseDefense,baseCritRate,baseHealth,baseSpeed,tags,skillIds,imageUrl,description,attackGrowth,magicAttackGrowth,defenseGrowth,critRateGrowth,healthGrowth,speedGrowth,maxLevel,evolutionCardId,evolutionRequiredLevel,evolutionRequiredCards
CARD_001,火龍戰士,Dragon,3,120,80,60,0.1,300,15,Warrior;Fire,SKILL_001,dragon_warrior.png,強大的火龍戰士,8,5,4,0.002,20,1,60,,0,0
CARD_002,水精靈,Elf,2,60,120,40,0.08,200,20,Mage;Water,SKILL_002,water_elf.png,優雅的水系精靈,4,8,3,0.003,15,2,50,,0,0
CARD_003,地騎士,Human,2,100,40,80,0.05,350,12,Warrior;Earth,SKILL_003,earth_knight.png,堅韌的大地騎士,6,2,6,0.001,25,1,55,,0,0`;

      case 'StageConfig.csv':
        return `id,name,description,stageNumber,backgroundImageUrl,musicId,previousStageId,rewardGold,firstClearRewardId,recommendedPower,loopMonsterPoolDeckId,loopMinEnemiesPerBattle,loopMaxEnemiesPerBattle,loopDropPoolId,bossDeckId,bossDropPoolId
stage_1,森林深處,神秘的森林深處，充滿危險的生物,1,forest_bg.jpg,forest_theme,,100,REWARD_001,1000,DECK_FOREST,1,3,DROP_FOREST,DECK_FOREST_BOSS,DROP_BOSS`;

      case 'SkillConfig.csv':
        return `id,name,description,targetType,effectType,baseValue,valueGrowth,cooldown,animationId,soundId,particleEffectId,statusEffectId,statusEffectDuration,isPassive,triggerCondition,priority
SKILL_001,火焰斬擊,強力的火焰攻擊,enemy_single,damage,100,10,0,fire_slash,sword_hit,fire_particles,,0,false,,1
SKILL_002,治療術,恢復友軍生命值,ally_single,heal,80,8,2,heal_light,heal_sound,heal_particles,,0,false,,2
SKILL_003,大地護盾,提升防禦力,self,buff_defense,50,5,3,earth_shield,shield_sound,earth_particles,DEF_BUFF,3,false,,3`;

      default:
        return `id,name
DEFAULT_001,默認項目`;
    }
  }

  /**
   * 解析 CSV 內容
   */
  private parseCSV<T>(csvContent: string, filename: string): CSVParseResult<T> {
    try {
      const lines = csvContent.split('\n').filter(line => line.trim());
      if (lines.length < 2) {
        return {
          success: false,
          data: [],
          errors: [`${filename} 格式錯誤: 至少需要標題行和一行數據`],
          warnings: []
        };
      }

      // 解析標題行
      const headers = lines[0].split(',').map(h => h.trim());
      const data: T[] = [];
      const errors: string[] = [];
      const warnings: string[] = [];

      // 解析數據行
      for (let i = 1; i < lines.length; i++) {
        try {
          const values = lines[i].split(',').map(v => v.trim());
          const row: any = {};

          headers.forEach((header, index) => {
            const value = values[index] || '';
            row[header] = this.parseValue(value, header);
          });

          data.push(row as T);
        } catch (error) {
          errors.push(`第 ${i + 1} 行解析錯誤: ${error}`);
        }
      }

      return {
        success: errors.length === 0,
        data,
        errors,
        warnings
      };

    } catch (error) {
      return {
        success: false,
        data: [],
        errors: [`CSV 解析失敗: ${error}`],
        warnings: []
      };
    }
  }

  /**
   * 解析單個值
   */
  private parseValue(value: string, fieldName: string): any {
    if (!value) return '';

    // 數字類型
    if (fieldName.includes('attack') || fieldName.includes('health') || 
        fieldName.includes('speed') || fieldName.includes('level') ||
        fieldName.includes('cost') || fieldName.includes('rate') ||
        fieldName.includes('count') || fieldName.includes('power')) {
      const num = parseFloat(value);
      return isNaN(num) ? 0 : num;
    }

    // 布林類型
    if (fieldName.includes('is') || fieldName.includes('active') || fieldName.includes('passive')) {
      return value.toLowerCase() === 'true' || value === '1';
    }

    // 陣列類型 (用分號分隔)
    if (fieldName.includes('ids') || fieldName.includes('tags') || fieldName.includes('race')) {
      return value ? value.split(';').map(v => v.trim()).filter(v => v) : [];
    }

    // 字符串類型
    return value;
  }

  /**
   * 處理配置載入結果
   */
  private processConfigResults<T extends { id: string }>(
    configType: string,
    result: CSVParseResult<T>,
    targetMap: Map<string, T>
  ): void {
    if (!result.success) {
      console.error(`❌ ${configType} 配置載入失敗:`, result.errors);
      return;
    }

    result.data.forEach(item => {
      targetMap.set(item.id, item);
    });

    if (result.warnings.length > 0) {
      console.warn(`⚠️ ${configType} 配置警告:`, result.warnings);
    }

    console.log(`✅ ${configType} 配置載入成功: ${result.data.length} 項`);
  }

  /**
   * 驗證配置完整性
   */
  private validateConfigs(): ConfigValidationResult {
    const errors: any[] = [];
    const warnings: any[] = [];

    // 這裡可以添加更多驗證邏輯
    // 例如檢查引用完整性、數值範圍等

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // 配置獲取方法
  getCardConfig(id: string): CardConfig | undefined {
    return this.cards.get(id);
  }

  getSkillConfig(id: string): SkillConfig | undefined {
    return this.skills.get(id);
  }

  getStageConfig(id: string): StageConfig | undefined {
    return this.stages.get(id);
  }

  getEnemyDeckConfig(id: string): EnemyDeckConfig | undefined {
    return this.enemyDecks.get(id);
  }

  getDropConfig(id: string): DropConfig | undefined {
    return this.drops.get(id);
  }

  getGachaConfig(id: string): GachaConfig | undefined {
    return this.gacha.get(id);
  }

  getAIBehaviorConfig(id: string): AIBehaviorConfig | undefined {
    return this.aiBehaviors.get(id);
  }

  /**
   * 獲取所有配置的統計信息
   */
  getConfigStats() {
    return {
      cards: this.cards.size,
      skills: this.skills.size,
      stages: this.stages.size,
      enemyDecks: this.enemyDecks.size,
      drops: this.drops.size,
      gacha: this.gacha.size,
      aiBehaviors: this.aiBehaviors.size,
      isLoaded: this.isLoaded
    };
  }
}

// 全局配置管理器實例
export const configManager = new GameConfigManager();
