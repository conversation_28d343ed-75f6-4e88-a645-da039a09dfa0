import * as React from 'react';
import { Text } from 'react-native';
export type Props = React.ComponentProps<typeof Text> & {
    children: React.ReactNode;
};
/**
 * @deprecated Deprecated in v5.x - use `<Text variant="titleLarge" />` instead.
 * Typography component for showing a title.
 *
 * <div class="screenshots">
 *   <img src="screenshots/title.png" />
 * </div>
 *
 * ## Usage
 * ```js
 * import * as React from 'react';
 * import { Title } from 'react-native-paper';
 *
 * const MyComponent = () => (
 *   <Title>Title</Title>
 * );
 *
 * export default MyComponent;
 * ```
 */
declare const Title: (props: Props) => React.JSX.Element;
export default Title;
//# sourceMappingURL=Title.d.ts.map