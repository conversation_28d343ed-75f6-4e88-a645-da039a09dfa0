/**
 * 測試配置載入器
 */

const { GameConfigManager } = require('./services/ConfigManager');

async function testConfigManager() {
  console.log('🧪 開始測試配置管理器...');
  
  const configManager = new GameConfigManager();
  
  try {
    await configManager.loadConfigs();
    
    const stats = configManager.getConfigStats();
    console.log('📊 配置統計:', stats);
    
    // 測試獲取卡牌配置
    const card = configManager.getCardConfig('CARD_001');
    if (card) {
      console.log('✅ 成功獲取卡牌配置:', card.name);
    } else {
      console.log('❌ 無法獲取卡牌配置');
    }
    
    // 測試獲取關卡配置
    const stage = configManager.getStageConfig('stage_1');
    if (stage) {
      console.log('✅ 成功獲取關卡配置:', stage.name);
    } else {
      console.log('❌ 無法獲取關卡配置');
    }
    
    console.log('✅ 配置管理器測試完成');
    
  } catch (error) {
    console.error('❌ 配置管理器測試失敗:', error);
  }
}

testConfigManager();
