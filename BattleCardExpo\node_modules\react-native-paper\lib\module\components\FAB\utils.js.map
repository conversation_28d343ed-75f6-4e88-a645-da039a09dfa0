{"version": 3, "names": ["I18nManager", "Platform", "color", "black", "white", "getContrastingColor", "getCombinedStyles", "isAnimatedFromRight", "isIconStatic", "distance", "animFAB", "isRTL", "defaultPositionStyles", "left", "right", "undefined", "combinedStyles", "innerWrapper", "iconWrapper", "absoluteFill", "animatedFromRight", "animatedFromRightRTL", "animatedFromLeft", "animatedFromLeftRTL", "transform", "translateX", "interpolate", "inputRange", "outputRange", "Math", "abs", "getBackgroundColor", "theme", "isVariant", "disabled", "customBackgroundColor", "_theme$colors", "isV3", "colors", "surfaceDisabled", "primaryContainer", "secondaryContainer", "tertiaryContainer", "elevation", "level3", "dark", "alpha", "rgb", "string", "accent", "getForegroundColor", "backgroundColor", "customColor", "onSurfaceDisabled", "onPrimaryContainer", "onSecondaryContainer", "onTertiaryContainer", "primary", "getFABColors", "variant", "customRippleColor", "variantToCompare", "baseFABColorProps", "foregroundColor", "rippleColor", "getLabelColor", "onSurface", "text", "fade", "getBackdropColor", "customBackdropColor", "_theme$colors2", "background", "backdrop", "getStackedFABBackgroundColor", "surface", "getFABGroupColors", "labelColor", "backdropColor", "stackedFABBackgroundColor", "standardSize", "height", "width", "borderRadius", "smallSize", "v3SmallSize", "v3MediumSize", "v3LargeSize", "getCustomFabSize", "customSize", "roundness", "getFabStyle", "size", "extended", "paddingHorizontal", "v3Extended", "getExtendedFabDimensions", "getExtendedFabStyle", "cachedContext", "getCanvasContext", "canvas", "document", "createElement", "getContext", "getLabelSizeWeb", "ref", "OS", "current", "canvasContext", "elementStyles", "window", "getComputedStyle", "font", "metrics", "measureText", "innerText", "fontBoundingBoxAscent", "fontBoundingBoxDescent"], "sourceRoot": "../../../../src", "sources": ["components/FAB/utils.ts"], "mappings": "AACA,SAGEA,WAAW,EACXC,QAAQ,QAEH,cAAc;AAErB,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,KAAK,EAAEC,KAAK,QAAQ,+BAA+B;AAE5D,OAAOC,mBAAmB,MAAM,iCAAiC;AAuBjE,OAAO,MAAMC,iBAAiB,GAAGA,CAAC;EAChCC,mBAAmB;EACnBC,YAAY;EACZC,QAAQ;EACRC;AACsB,CAAC,KAAqB;EAC5C,MAAM;IAAEC;EAAM,CAAC,GAAGX,WAAW;EAE7B,MAAMY,qBAAqB,GAAG;IAAEC,IAAI,EAAE,CAACJ,QAAQ;IAAEK,KAAK,EAAEC;EAAU,CAAC;EAEnE,MAAMC,cAA8B,GAAG;IACrCC,YAAY,EAAE;MACZ,GAAGL;IACL,CAAC;IACDM,WAAW,EAAE;MACX,GAAGN;IACL,CAAC;IACDO,YAAY,EAAE,CAAC;EACjB,CAAC;EAED,MAAMC,iBAAiB,GAAGb,mBAAmB,IAAI,CAACI,KAAK;EACvD,MAAMU,oBAAoB,GAAGd,mBAAmB,IAAII,KAAK;EACzD,MAAMW,gBAAgB,GAAG,CAACf,mBAAmB,IAAI,CAACI,KAAK;EACvD,MAAMY,mBAAmB,GAAG,CAAChB,mBAAmB,IAAII,KAAK;EAEzD,IAAIS,iBAAiB,EAAE;IACrBJ,cAAc,CAACC,YAAY,CAACO,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEf,OAAO,CAACgB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAAClB,QAAQ,EAAE,CAAC,CAAC;QACzBmB,WAAW,EAAE,CAACnB,QAAQ,EAAE,CAAC;MAC3B,CAAC;IACH,CAAC,CACF;IACDO,cAAc,CAACE,WAAW,CAACM,SAAS,GAAG,CACrC;MACEC,UAAU,EAAEjB,YAAY,GAAG,CAAC,GAAGE;IACjC,CAAC,CACF;IACDM,cAAc,CAACG,YAAY,CAACK,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEf,OAAO,CAACgB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAAClB,QAAQ,EAAE,CAAC,CAAC;QACzBmB,WAAW,EAAE,CAACC,IAAI,CAACC,GAAG,CAACrB,QAAQ,CAAC,GAAG,CAAC,EAAEoB,IAAI,CAACC,GAAG,CAACrB,QAAQ,CAAC;MAC1D,CAAC;IACH,CAAC,CACF;EACH,CAAC,MAAM,IAAIY,oBAAoB,EAAE;IAC/BL,cAAc,CAACE,WAAW,CAACM,SAAS,GAAG,CACrC;MACEC,UAAU,EAAEjB,YAAY,GACpB,CAAC,GACDE,OAAO,CAACgB,WAAW,CAAC;QAClBC,UAAU,EAAE,CAAClB,QAAQ,EAAE,CAAC,CAAC;QACzBmB,WAAW,EAAE,CAAC,CAACnB,QAAQ,EAAE,CAAC;MAC5B,CAAC;IACP,CAAC,CACF;IACDO,cAAc,CAACC,YAAY,CAACO,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEf,OAAO,CAACgB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAAClB,QAAQ,EAAE,CAAC,CAAC;QACzBmB,WAAW,EAAE,CAAC,CAACnB,QAAQ,EAAE,CAAC;MAC5B,CAAC;IACH,CAAC,CACF;IACDO,cAAc,CAACG,YAAY,CAACK,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEf,OAAO,CAACgB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAAClB,QAAQ,EAAE,CAAC,CAAC;QACzBmB,WAAW,EAAE,CAAC,CAAC,EAAEnB,QAAQ;MAC3B,CAAC;IACH,CAAC,CACF;EACH,CAAC,MAAM,IAAIa,gBAAgB,EAAE;IAC3BN,cAAc,CAACE,WAAW,CAACM,SAAS,GAAG,CACrC;MACEC,UAAU,EAAEjB,YAAY,GACpBC,QAAQ,GACRC,OAAO,CAACgB,WAAW,CAAC;QAClBC,UAAU,EAAE,CAAC,CAAC,EAAElB,QAAQ,CAAC;QACzBmB,WAAW,EAAE,CAACnB,QAAQ,EAAEA,QAAQ,GAAG,CAAC;MACtC,CAAC;IACP,CAAC,CACF;IACDO,cAAc,CAACC,YAAY,CAACO,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEf;IACd,CAAC,CACF;IACDM,cAAc,CAACG,YAAY,CAACK,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEf,OAAO,CAACgB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAAC,CAAC,EAAElB,QAAQ,CAAC;QACzBmB,WAAW,EAAE,CAAC,CAAC,EAAEC,IAAI,CAACC,GAAG,CAACrB,QAAQ,CAAC,GAAG,CAAC;MACzC,CAAC;IACH,CAAC,CACF;EACH,CAAC,MAAM,IAAIc,mBAAmB,EAAE;IAC9BP,cAAc,CAACE,WAAW,CAACM,SAAS,GAAG,CACrC;MACEC,UAAU,EAAEjB,YAAY,GACpBE,OAAO,CAACgB,WAAW,CAAC;QAClBC,UAAU,EAAE,CAAC,CAAC,EAAElB,QAAQ,CAAC;QACzBmB,WAAW,EAAE,CAAC,CAACnB,QAAQ,EAAE,CAACA,QAAQ,GAAG,CAAC;MACxC,CAAC,CAAC,GACF,CAACA;IACP,CAAC,CACF;IACDO,cAAc,CAACC,YAAY,CAACO,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEf,OAAO,CAACgB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAAC,CAAC,EAAElB,QAAQ,CAAC;QACzBmB,WAAW,EAAE,CAAC,CAAC,EAAE,CAACnB,QAAQ;MAC5B,CAAC;IACH,CAAC,CACF;IACDO,cAAc,CAACG,YAAY,CAACK,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEf,OAAO,CAACgB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAAC,CAAC,EAAElB,QAAQ,CAAC;QACzBmB,WAAW,EAAE,CAAC,CAAC,EAAE,CAACnB,QAAQ;MAC5B,CAAC;IACH,CAAC,CACF;EACH;EAEA,OAAOO,cAAc;AACvB,CAAC;AAED,MAAMe,kBAAkB,GAAGA,CAAC;EAC1BC,KAAK;EACLC,SAAS;EACTC,QAAQ;EACRC;AACkD,CAAC,KAAK;EAAA,IAAAC,aAAA;EACxD,IAAID,qBAAqB,IAAI,CAACD,QAAQ,EAAE;IACtC,OAAOC,qBAAqB;EAC9B;EAEA,IAAIH,KAAK,CAACK,IAAI,EAAE;IACd,IAAIH,QAAQ,EAAE;MACZ,OAAOF,KAAK,CAACM,MAAM,CAACC,eAAe;IACrC;IAEA,IAAIN,SAAS,CAAC,SAAS,CAAC,EAAE;MACxB,OAAOD,KAAK,CAACM,MAAM,CAACE,gBAAgB;IACtC;IAEA,IAAIP,SAAS,CAAC,WAAW,CAAC,EAAE;MAC1B,OAAOD,KAAK,CAACM,MAAM,CAACG,kBAAkB;IACxC;IAEA,IAAIR,SAAS,CAAC,UAAU,CAAC,EAAE;MACzB,OAAOD,KAAK,CAACM,MAAM,CAACI,iBAAiB;IACvC;IAEA,IAAIT,SAAS,CAAC,SAAS,CAAC,EAAE;MACxB,OAAOD,KAAK,CAACM,MAAM,CAACK,SAAS,CAACC,MAAM;IACtC;EACF;EAEA,IAAIV,QAAQ,EAAE;IACZ,IAAIF,KAAK,CAACa,IAAI,EAAE;MACd,OAAO3C,KAAK,CAACE,KAAK,CAAC,CAAC0C,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IAChD;IACA,OAAO9C,KAAK,CAACC,KAAK,CAAC,CAAC2C,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAChD;;EAEA;EACA,QAAAZ,aAAA,GAAOJ,KAAK,CAACM,MAAM,cAAAF,aAAA,uBAAZA,aAAA,CAAca,MAAM;AAC7B,CAAC;AAED,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BlB,KAAK;EACLC,SAAS;EACTC,QAAQ;EACRiB,eAAe;EACfC;AAC6D,CAAC,KAAK;EACnE,IAAI,OAAOA,WAAW,KAAK,WAAW,IAAI,CAAClB,QAAQ,EAAE;IACnD,OAAOkB,WAAW;EACpB;EAEA,IAAIpB,KAAK,CAACK,IAAI,EAAE;IACd,IAAIH,QAAQ,EAAE;MACZ,OAAOF,KAAK,CAACM,MAAM,CAACe,iBAAiB;IACvC;IAEA,IAAIpB,SAAS,CAAC,SAAS,CAAC,EAAE;MACxB,OAAOD,KAAK,CAACM,MAAM,CAACgB,kBAAkB;IACxC;IAEA,IAAIrB,SAAS,CAAC,WAAW,CAAC,EAAE;MAC1B,OAAOD,KAAK,CAACM,MAAM,CAACiB,oBAAoB;IAC1C;IAEA,IAAItB,SAAS,CAAC,UAAU,CAAC,EAAE;MACzB,OAAOD,KAAK,CAACM,MAAM,CAACkB,mBAAmB;IACzC;IAEA,IAAIvB,SAAS,CAAC,SAAS,CAAC,EAAE;MACxB,OAAOD,KAAK,CAACM,MAAM,CAACmB,OAAO;IAC7B;EACF;EAEA,IAAIvB,QAAQ,EAAE;IACZ,IAAIF,KAAK,CAACa,IAAI,EAAE;MACd,OAAO3C,KAAK,CAACE,KAAK,CAAC,CAAC0C,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IAChD;IACA,OAAO9C,KAAK,CAACC,KAAK,CAAC,CAAC2C,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAChD;EAEA,IAAIG,eAAe,EAAE;IACnB,OAAO9C,mBAAmB,CACxB8C,eAAe,IAAI/C,KAAK,EACxBA,KAAK,EACL,oBACF,CAAC;EACH;EAEA,OAAOC,mBAAmB,CAACD,KAAK,EAAEA,KAAK,EAAE,oBAAoB,CAAC;AAChE,CAAC;AAED,OAAO,MAAMsD,YAAY,GAAGA,CAAC;EAC3B1B,KAAK;EACL2B,OAAO;EACPzB,QAAQ;EACRkB,WAAW;EACXjB,qBAAqB;EACrByB;AAQF,CAAC,KAAK;EACJ,MAAM3B,SAAS,GAAI4B,gBAAyB,IAAK;IAC/C,OAAOF,OAAO,KAAKE,gBAAgB;EACrC,CAAC;EAED,MAAMC,iBAAiB,GAAG;IAAE9B,KAAK;IAAEC,SAAS;IAAEC;EAAS,CAAC;EAExD,MAAMiB,eAAe,GAAGpB,kBAAkB,CAAC;IACzC,GAAG+B,iBAAiB;IACpB3B;EACF,CAAC,CAAC;EAEF,MAAM4B,eAAe,GAAGb,kBAAkB,CAAC;IACzC,GAAGY,iBAAiB;IACpBV,WAAW;IACXD;EACF,CAAC,CAAC;EAEF,OAAO;IACLA,eAAe;IACfY,eAAe;IACfC,WAAW,EACTJ,iBAAiB,IAAI1D,KAAK,CAAC6D,eAAe,CAAC,CAACjB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC;EACzE,CAAC;AACH,CAAC;AAED,MAAMiB,aAAa,GAAGA,CAAC;EAAEjC;AAAgC,CAAC,KAAK;EAC7D,IAAIA,KAAK,CAACK,IAAI,EAAE;IACd,OAAOL,KAAK,CAACM,MAAM,CAAC4B,SAAS;EAC/B;EAEA,IAAIlC,KAAK,CAACa,IAAI,EAAE;IACd,OAAOb,KAAK,CAACM,MAAM,CAAC6B,IAAI;EAC1B;EAEA,OAAOjE,KAAK,CAAC8B,KAAK,CAACM,MAAM,CAAC6B,IAAI,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAACrB,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AAC3D,CAAC;AAED,MAAMqB,gBAAgB,GAAGA,CAAC;EACxBrC,KAAK;EACLsC;AAIF,CAAC,KAAK;EAAA,IAAAC,cAAA;EACJ,IAAID,mBAAmB,EAAE;IACvB,OAAOA,mBAAmB;EAC5B;EACA,IAAItC,KAAK,CAACK,IAAI,EAAE;IACd,OAAOnC,KAAK,CAAC8B,KAAK,CAACM,MAAM,CAACkC,UAAU,CAAC,CAAC1B,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAClE;EACA,QAAAuB,cAAA,GAAOvC,KAAK,CAACM,MAAM,cAAAiC,cAAA,uBAAZA,cAAA,CAAcE,QAAQ;AAC/B,CAAC;AAED,MAAMC,4BAA4B,GAAGA,CAAC;EAAE1C;AAAgC,CAAC,KAAK;EAC5E,IAAIA,KAAK,CAACK,IAAI,EAAE;IACd,OAAOL,KAAK,CAACM,MAAM,CAACK,SAAS,CAACC,MAAM;EACtC;EACA,OAAOZ,KAAK,CAACM,MAAM,CAACqC,OAAO;AAC7B,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAGA,CAAC;EAChC5C,KAAK;EACLsC;AAIF,CAAC,KAAK;EACJ,OAAO;IACLO,UAAU,EAAEZ,aAAa,CAAC;MAAEjC;IAAM,CAAC,CAAC;IACpC8C,aAAa,EAAET,gBAAgB,CAAC;MAAErC,KAAK;MAAEsC;IAAoB,CAAC,CAAC;IAC/DS,yBAAyB,EAAEL,4BAA4B,CAAC;MAAE1C;IAAM,CAAC;EACnE,CAAC;AACH,CAAC;AAED,MAAMgD,YAAY,GAAG;EACnBC,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE,EAAE;EACTC,YAAY,EAAE;AAChB,CAAC;AACD,MAAMC,SAAS,GAAG;EAChBH,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE,EAAE;EACTC,YAAY,EAAE;AAChB,CAAC;AACD,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE;AACT,CAAC;AACD,MAAMI,YAAY,GAAG;EACnBL,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE;AACT,CAAC;AACD,MAAMK,WAAW,GAAG;EAClBN,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE;AACT,CAAC;AAED,MAAMM,gBAAgB,GAAGA,CAACC,UAAkB,EAAEC,SAAiB,MAAM;EACnET,MAAM,EAAEQ,UAAU;EAClBP,KAAK,EAAEO,UAAU;EACjBN,YAAY,EAAEO,SAAS,KAAK,CAAC,GAAG,CAAC,GAAGD,UAAU,GAAGC;AACnD,CAAC,CAAC;AAEF,OAAO,MAAMC,WAAW,GAAGA,CAAC;EAC1BC,IAAI;EACJ5D,KAAK;EACLyD;AAKF,CAAC,KAAK;EACJ,MAAM;IAAEpD,IAAI;IAAEqD;EAAU,CAAC,GAAG1D,KAAK;EAEjC,IAAIyD,UAAU,EAAE,OAAOD,gBAAgB,CAACC,UAAU,EAAEC,SAAS,CAAC;EAE9D,IAAIrD,IAAI,EAAE;IACR,QAAQuD,IAAI;MACV,KAAK,OAAO;QACV,OAAO;UAAE,GAAGP,WAAW;UAAEF,YAAY,EAAE,CAAC,GAAGO;QAAU,CAAC;MACxD,KAAK,QAAQ;QACX,OAAO;UAAE,GAAGJ,YAAY;UAAEH,YAAY,EAAE,CAAC,GAAGO;QAAU,CAAC;MACzD,KAAK,OAAO;QACV,OAAO;UAAE,GAAGH,WAAW;UAAEJ,YAAY,EAAE,CAAC,GAAGO;QAAU,CAAC;IAC1D;EACF;EAEA,IAAIE,IAAI,KAAK,OAAO,EAAE;IACpB,OAAOR,SAAS;EAClB;EACA,OAAOJ,YAAY;AACrB,CAAC;AAED,MAAMa,QAAQ,GAAG;EACfZ,MAAM,EAAE,EAAE;EACVa,iBAAiB,EAAE;AACrB,CAAC;AAED,MAAMC,UAAU,GAAG;EACjBd,MAAM,EAAE,EAAE;EACVE,YAAY,EAAE,EAAE;EAChBW,iBAAiB,EAAE;AACrB,CAAC;AAED,MAAME,wBAAwB,GAAIP,UAAkB,KAAM;EACxDR,MAAM,EAAEQ,UAAU;EAClBK,iBAAiB,EAAE;AACrB,CAAC,CAAC;AAEF,OAAO,MAAMG,mBAAmB,GAAGA,CAAC;EAClCR,UAAU;EACVzD;AAIF,CAAC,KAAK;EACJ,IAAIyD,UAAU,EAAE,OAAOO,wBAAwB,CAACP,UAAU,CAAC;EAE3D,MAAM;IAAEpD;EAAK,CAAC,GAAGL,KAAK;EAEtB,OAAOK,IAAI,GAAG0D,UAAU,GAAGF,QAAQ;AACrC,CAAC;AAED,IAAIK,aAA8C,GAAG,IAAI;AAEzD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAC7B,IAAID,aAAa,EAAE;IACjB,OAAOA,aAAa;EACtB;EAEA,MAAME,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/CJ,aAAa,GAAGE,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;EAEvC,OAAOL,aAAa;AACtB,CAAC;AAED,OAAO,MAAMM,eAAe,GAAIC,GAAyC,IAAK;EAC5E,IAAIxG,QAAQ,CAACyG,EAAE,KAAK,KAAK,IAAID,GAAG,CAACE,OAAO,KAAK,IAAI,EAAE;IACjD,OAAO,IAAI;EACb;EAEA,MAAMC,aAAa,GAAGT,gBAAgB,CAAC,CAAC;EAExC,IAAI,CAACS,aAAa,EAAE;IAClB,OAAO,IAAI;EACb;EAEA,MAAMC,aAAa,GAAGC,MAAM,CAACC,gBAAgB,CAACN,GAAG,CAACE,OAAO,CAAC;EAC1DC,aAAa,CAACI,IAAI,GAAGH,aAAa,CAACG,IAAI;EAEvC,MAAMC,OAAO,GAAGL,aAAa,CAACM,WAAW,CAACT,GAAG,CAACE,OAAO,CAACQ,SAAS,CAAC;EAEhE,OAAO;IACLjC,KAAK,EAAE+B,OAAO,CAAC/B,KAAK;IACpBD,MAAM,EACJ,CAACgC,OAAO,CAACG,qBAAqB,IAAI,CAAC,KAClCH,OAAO,CAACI,sBAAsB,IAAI,CAAC;EACxC,CAAC;AACH,CAAC", "ignoreList": []}