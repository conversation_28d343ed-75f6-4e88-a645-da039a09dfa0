/**
 * 🔄 載入畫面組件
 * 
 * 在配置載入期間顯示的載入畫面
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  SafeAreaView,
  StatusBar
} from 'react-native';

interface LoadingScreenProps {
  message?: string;
  progress?: number;
  error?: string | null;
}

export default function LoadingScreen({ 
  message = '載入遊戲配置中...', 
  progress,
  error 
}: LoadingScreenProps) {
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#f7f3e9" />
      
      <View style={styles.content}>
        {/* 遊戲標題 */}
        <View style={styles.titleContainer}>
          <Text style={styles.gameTitle}>🃏 PetingGame</Text>
          <Text style={styles.subtitle}>自動戰鬥卡牌遊戲</Text>
        </View>

        {/* 載入指示器 */}
        <View style={styles.loadingContainer}>
          {error ? (
            <>
              <Text style={styles.errorIcon}>❌</Text>
              <Text style={styles.errorText}>{error}</Text>
              <Text style={styles.retryText}>請重新啟動應用</Text>
            </>
          ) : (
            <>
              <ActivityIndicator 
                size="large" 
                color="#8b4513" 
                style={styles.spinner}
              />
              <Text style={styles.loadingText}>{message}</Text>
              
              {progress !== undefined && (
                <View style={styles.progressContainer}>
                  <View style={styles.progressBar}>
                    <View 
                      style={[
                        styles.progressFill, 
                        { width: `${Math.max(0, Math.min(100, progress))}%` }
                      ]} 
                    />
                  </View>
                  <Text style={styles.progressText}>
                    {Math.round(progress)}%
                  </Text>
                </View>
              )}
            </>
          )}
        </View>

        {/* 底部提示 */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            🎮 準備進入精彩的卡牌世界
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f7f3e9',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 60,
  },
  gameTitle: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#8b4513',
    fontFamily: 'Times New Roman',
    transform: [{ rotate: '-2deg' }],
    textShadowColor: 'rgba(139, 69, 19, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#8b4513',
    fontFamily: 'Times New Roman',
    transform: [{ rotate: '1deg' }],
    opacity: 0.8,
  },
  loadingContainer: {
    alignItems: 'center',
    marginBottom: 60,
  },
  spinner: {
    marginBottom: 20,
  },
  loadingText: {
    fontSize: 18,
    color: '#8b4513',
    fontFamily: 'Times New Roman',
    textAlign: 'center',
    marginBottom: 20,
  },
  progressContainer: {
    width: 200,
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: 'rgba(139, 69, 19, 0.2)',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#f4a261',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    color: '#8b4513',
    fontFamily: 'Times New Roman',
    fontWeight: 'bold',
  },
  errorIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 18,
    color: '#dc2626',
    fontFamily: 'Times New Roman',
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  retryText: {
    fontSize: 14,
    color: '#8b4513',
    fontFamily: 'Times New Roman',
    textAlign: 'center',
    opacity: 0.7,
  },
  footer: {
    position: 'absolute',
    bottom: 40,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#8b4513',
    fontFamily: 'Times New Roman',
    opacity: 0.6,
    transform: [{ rotate: '-1deg' }],
  },
});
