import React from 'react';
import { Alert } from 'react-native';
import { useRouter } from 'expo-router';
import MainEntry from '@/components/MainEntry';

export default function HomeScreen() {
  const router = useRouter();

  const handleStartBattle = () => {
    // 這裡可以導航到戰鬥頁面
    Alert.alert(
      '戰鬥系統',
      '戰鬥頁面尚未實現，將在後續版本中添加',
      [{ text: '確定' }]
    );
  };

  const handleNavigateToTab = (tab: string) => {
    // 這裡可以處理標籤導航
    console.log('導航到標籤:', tab);
    Alert.alert(
      '導航',
      `${tab} 頁面尚未實現，將在後續版本中添加`,
      [{ text: '確定' }]
    );
  };

  return (
    <MainEntry
      onStartBattle={handleStartBattle}
      onNavigateToTab={handleNavigateToTab}
    />
  );
}


