import React from 'react';
import { Alert } from 'react-native';
import { useRouter } from 'expo-router';
import MainEntry from '@/components/MainEntry';

export default function HomeScreen() {
  const router = useRouter();

  const handleStartBattle = () => {
    // Navigate to battle screen with preview
    router.push('/(tabs)/battle');
  };

  const handleNavigateToTab = (tab: string) => {
    // 這裡可以處理標籤導航
    console.log('導航到標籤:', tab);
    Alert.alert(
      '導航',
      `${tab} 頁面尚未實現，將在後續版本中添加`,
      [{ text: '確定' }]
    );
  };

  return (
    <MainEntry
      onStartBattle={handleStartBattle}
      onNavigateToTab={handleNavigateToTab}
    />
  );
}


