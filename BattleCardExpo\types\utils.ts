/**
 * 🛠️ 類型工具函數
 * 
 * 提供類型守衛、類型檢查和類型轉換等工具函數
 * 幫助在運行時進行類型安全檢查
 */

import {
    AIBehaviorType,
    BattleActionType,
    BattleCard,
    BattleState,
    BattleTag,
    Card,
    CardRace,
    CardRarity,
    ElementTag,
    SkillEffectType,
    SkillTargetType,
    SpecialTag
} from './index';

// ==================== 類型守衛函數 ====================

/**
 * 檢查是否為有效的卡牌對象
 */
export function isCard(obj: any): obj is Card {
  return obj &&
    typeof obj.id === 'string' &&
    typeof obj.configId === 'string' &&
    typeof obj.name === 'string' &&
    Array.isArray(obj.race) && obj.race.every((r: any) => Object.values(CardRace).includes(r)) &&
    Object.values(CardRarity).includes(obj.rarity) &&
    typeof obj.level === 'number' &&
    typeof obj.experience === 'number' &&
    obj.stats &&
    typeof obj.stats.attack === 'number' &&
    typeof obj.stats.magicAttack === 'number' &&
    typeof obj.stats.defense === 'number' &&
    typeof obj.stats.critRate === 'number' &&
    typeof obj.stats.health === 'number' &&
    typeof obj.stats.speed === 'number' &&
    Array.isArray(obj.tags) &&
    Array.isArray(obj.skills) &&
    typeof obj.isOwned === 'boolean';
}

/**
 * 檢查是否為戰鬥卡牌對象
 */
export function isBattleCard(obj: any): obj is BattleCard {
  if (!isCard(obj)) return false;
  const o = obj as any;
  return typeof o.currentHealth === 'number' &&
    typeof o.actionBar === 'number' &&
    Array.isArray(o.buffs) &&
    Array.isArray(o.debuffs) &&
    typeof o.isAlive === 'boolean' &&
    o.position && typeof o.position.row === 'number' && typeof o.position.col === 'number' &&
    (o.team === 'player' || o.team === 'enemy');
}

/**
 * 檢查是否為有效的稀有度
 */
export function isValidRarity(value: any): value is CardRarity {
  return Object.values(CardRarity).includes(value);
}

/**
 * 檢查是否為有效的種族
 */
export function isValidRace(value: any): value is CardRace {
  return Object.values(CardRace).includes(value);
}

/**
 * 檢查是否為有效的戰鬥標籤
 */
export function isValidBattleTag(value: any): value is BattleTag {
  return Object.values(BattleTag).includes(value);
}

/**
 * 檢查是否為有效的元素標籤
 */
export function isValidElementTag(value: any): value is ElementTag {
  return Object.values(ElementTag).includes(value);
}

/**
 * 檢查是否為有效的特殊標籤
 */
export function isValidSpecialTag(value: any): value is SpecialTag {
  return Object.values(SpecialTag).includes(value);
}

/**
 * 檢查是否為有效的技能目標類型
 */
export function isValidSkillTargetType(value: any): value is SkillTargetType {
  return Object.values(SkillTargetType).includes(value);
}

/**
 * 檢查是否為有效的技能效果類型
 */
export function isValidSkillEffectType(value: any): value is SkillEffectType {
  return Object.values(SkillEffectType).includes(value);
}

/**
 * 檢查是否為有效的AI行為類型
 */
export function isValidAIBehaviorType(value: any): value is AIBehaviorType {
  return Object.values(AIBehaviorType).includes(value);
}

/**
 * 檢查是否為有效的戰鬥狀態
 */
export function isValidBattleState(value: any): value is BattleState {
  return Object.values(BattleState).includes(value);
}

/**
 * 檢查是否為有效的戰鬥行動類型
 */
export function isValidBattleActionType(value: any): value is BattleActionType {
  return Object.values(BattleActionType).includes(value);
}

// ==================== 類型轉換函數 ====================

/**
 * 安全地將字符串轉換為稀有度枚舉
 */
export function parseRarity(value: string | number): CardRarity | null {
  if (typeof value === 'number') {
    if (value >= 1 && value <= 5) {
      return value as CardRarity;
    }
  } else if (typeof value === 'string') {
    const numValue = parseInt(value, 10);
    if (numValue >= 1 && numValue <= 5) {
      return numValue as CardRarity;
    }
  }
  return null;
}

/**
 * 安全地將字符串轉換為種族枚舉
 */
export function parseRace(value: string): CardRace | null {
  const race = Object.values(CardRace).find(r => r === value);
  return race || null;
}

/**
 * 解析逗號分隔的標籤字符串為標籤數組
 */
export function parseTags<T>(
  value: string, 
  validValues: T[]
): T[] {
  if (!value || typeof value !== 'string') {
    return [];
  }
  
  return value
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => validValues.includes(tag as T))
    .map(tag => tag as T);
}

/**
 * 解析戰鬥標籤
 */
export function parseBattleTags(value: string): BattleTag[] {
  return parseTags(value, Object.values(BattleTag));
}

/**
 * 解析元素標籤
 */
export function parseElementTags(value: string): ElementTag[] {
  return parseTags(value, Object.values(ElementTag));
}

/**
 * 解析特殊標籤
 */
export function parseSpecialTags(value: string): SpecialTag[] {
  return parseTags(value, Object.values(SpecialTag));
}

/**
 * 將標籤數組轉換為逗號分隔的字符串
 */
export function tagsToString<T>(tags: T[]): string {
  return tags.join(',');
}

// ==================== 數值驗證函數 ====================

/**
 * 驗證數值是否在指定範圍內
 */
export function isInRange(value: number, min: number, max: number): boolean {
  return value >= min && value <= max;
}

/**
 * 驗證是否為正整數
 */
export function isPositiveInteger(value: any): value is number {
  return typeof value === 'number' && 
    Number.isInteger(value) && 
    value > 0;
}

/**
 * 驗證是否為非負數
 */
export function isNonNegativeNumber(value: any): value is number {
  return typeof value === 'number' && 
    !isNaN(value) && 
    value >= 0;
}

/**
 * 驗證機率值 (0-1)
 */
export function isValidProbability(value: any): value is number {
  return typeof value === 'number' && 
    !isNaN(value) && 
    value >= 0 && 
    value <= 1;
}

/**
 * 驗證百分比值 (0-100)
 */
export function isValidPercentage(value: any): value is number {
  return typeof value === 'number' && 
    !isNaN(value) && 
    value >= 0 && 
    value <= 100;
}

// ==================== 字符串驗證函數 ====================

/**
 * 驗證是否為非空字符串
 */
export function isNonEmptyString(value: any): value is string {
  return typeof value === 'string' && value.trim().length > 0;
}

/**
 * 驗證是否為有效的ID格式
 */
export function isValidId(value: any): value is string {
  return typeof value === 'string' && 
    /^[a-zA-Z0-9_-]+$/.test(value) && 
    value.length > 0 && 
    value.length <= 50;
}

/**
 * 驗證是否為有效的URL格式
 */
export function isValidUrl(value: any): value is string {
  if (typeof value !== 'string') return false;
  try {
    new URL(value);
    return true;
  } catch {
    return false;
  }
}

// ==================== 數組驗證函數 ====================

/**
 * 驗證是否為非空數組
 */
export function isNonEmptyArray<T>(value: any): value is T[] {
  return Array.isArray(value) && value.length > 0;
}

/**
 * 驗證數組中的所有元素是否都滿足條件
 */
export function allElementsMatch<T>(
  array: any[], 
  predicate: (item: any) => item is T
): array is T[] {
  return Array.isArray(array) && array.every(predicate);
}

// ==================== 日期驗證函數 ====================

/**
 * 驗證是否為有效的日期對象
 */
export function isValidDate(value: any): value is Date {
  return value instanceof Date && !isNaN(value.getTime());
}

/**
 * 驗證日期是否在未來
 */
export function isFutureDate(value: any): value is Date {
  return isValidDate(value) && value.getTime() > Date.now();
}

/**
 * 驗證日期是否在過去
 */
export function isPastDate(value: any): value is Date {
  return isValidDate(value) && value.getTime() < Date.now();
}

// ==================== 對象深度驗證函數 ====================

/**
 * 深度檢查對象是否具有指定的屬性結構
 */
export function hasRequiredProperties(
  obj: any, 
  properties: string[]
): boolean {
  if (!obj || typeof obj !== 'object') return false;
  
  return properties.every(prop => {
    const keys = prop.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (!current || typeof current !== 'object' || !(key in current)) {
        return false;
      }
      current = current[key];
    }
    
    return true;
  });
}

/**
 * 安全地獲取嵌套對象屬性
 */
export function safeGet<T>(
  obj: any, 
  path: string, 
  defaultValue: T
): T {
  const keys = path.split('.');
  let current = obj;
  
  for (const key of keys) {
    if (!current || typeof current !== 'object' || !(key in current)) {
      return defaultValue;
    }
    current = current[key];
  }
  
  return current as T;
}

// ==================== 錯誤處理工具 ====================

/**
 * 創建類型錯誤信息
 */
export function createTypeError(
  fieldName: string, 
  expectedType: string, 
  actualValue: any
): string {
  const actualType = typeof actualValue;
  return `Field '${fieldName}' expected ${expectedType}, but got ${actualType}: ${actualValue}`;
}

/**
 * 創建範圍錯誤信息
 */
export function createRangeError(
  fieldName: string, 
  min: number, 
  max: number, 
  actualValue: number
): string {
  return `Field '${fieldName}' must be between ${min} and ${max}, but got ${actualValue}`;
}
